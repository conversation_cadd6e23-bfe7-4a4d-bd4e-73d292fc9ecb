FROM btpns/openjdk11:latest AS builder

COPY target/*.jar /tmp/app/target/application.jar
RUN curl https://nexus.twprisma.com/repository/btpns-raw/elastic-apm/elastic-apm-agent.jar --output /tmp/elastic-apm-agent.jar

FROM btpns/openjdk11:latest

WORKDIR /opt/app

COPY --from=builder /tmp/app/target/application.jar ./
COPY --from=builder /tmp/elastic-apm-agent.jar ./

ENV PATH "$PATH:$JAVA_HOME/bin"

EXPOSE 8080 8081

ENTRYPOINT [ "sh", "-c", "java -javaagent:elastic-apm-agent.jar -Xss$HEAP_STACK -Xms$HEAP_MIN -Xmx$HEAP_MAX -jar application.jar" ]
