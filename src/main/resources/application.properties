spring.application.name=agendaku-fpb
spring.mvc.pathmatch.matching-strategy=ant_path_matcher
spring.cloud.config.import-check.enabled=false
logging.config=classpath:logback-spring.xml

# Transaction
spring.transaction.default-timeout=1000

# JPA
spring.jpa.show-sql=true
spring.jpa.hibernate.dialect=org.hibernate.dialect.SQLServer2012Dialect
spring.jpa.hibernate.ddl-auto=none
spring.jpa.properties.hibernate.format_sql=true
spring.datasource.driverClassName=com.microsoft.sqlserver.jdbc.SQLServerDriver

# to avoid using underscores for camelCasing of names
spring.jpa.hibernate.naming-strategy=org.hibernate.cfg.EJB3NamingStrategy
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl

# Datasource
spring.datasource.url=${DB_URL:*****************************************************************************}
spring.datasource.username=${DB_USER:nos_dev.dbo}
spring.datasource.password=${DB_PASS:Nos_d3v_dbo123!}

spring.datasource.hikari.connection-timeout=60000
spring.datasource.hikari.idle-timeout=300000
spring.datasource.hikari.minimum-idle=1
spring.datasource.hikari.maximum-pool-size=100

# Server
server.port=${SERVER_PORT:8080}
server.servlet.context-path=${CONTEXT_PATH:/dirty-fpb}

# MinIO Storage
storage.path=${IMAGE_STORAGE_PATH:https://storage-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com/bucket01/agendaku/fpb/}

# Kafka
spring.kafka.bootstrap-servers=${KAFKA_SERVER_URL:dirty-kafka-bootstrap.amq-streams-dev.svc.cluster.local:9093}
spring.kafka.consumer.auto-offset-reset=latest
spring.kafka.consumer.heartbeat-interval=20000
spring.kafka.consumer.properties.session.timeout.ms=60000
spring.kafka.consumer.enable-auto-commit=false
spring.kafka.consumer.client-id=kafka-agendaku-fpb
spring.kafka.consumer.auto-startup=${KAFKA_STATUP:false}
spring.kafka.key.truststore.location=${KAFKA_TRUSTSTORE_PATH:/usr/app/key/kafka-ca.jks}
spring.kafka.key.truststore.password=${KEYSTORE_PASSWORD:password}
spring.kafka.ssl.enabled=${KAFKA_SSL_ENABLED:true}
kafka.jaas.config=${JAAS_CONFIG:org.apache.kafka.common.security.scram.ScramLoginModule required username="agendaku-dirty" password="IZBoEZXRDlb9";}

#Kafka Topic
agendaku.kafka.topic.notification=agendaku.notification
agendaku.kafka.topic.notification.group=${agendaku.kafka.topic.notification}-001

#CM URL
agendaku.cm.post.lb=${CM_POST_LB_URL:https://cash-management-agendaku-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com/dev-cash-management/v1/lbDailyTransaction/save/force/0}

# Swagger toggle
springdoc.swagger-ui.enabled=${SPRINGDOC_UI_TOGGLE:false}
springdoc.api-docs.enabled=${SPRINGDOC_API_TOGGLE:false}

#File upload
spring.servlet.multipart.max-file-size=2MB
spring.servlet.multipart.max-request-size=2MB

retry.maxAttempts=5
retry.maxDelay=2000