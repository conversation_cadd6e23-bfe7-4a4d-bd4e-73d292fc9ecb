package com.btpnsyariah.agendaku.fpb.controller;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@CrossOrigin
@Slf4j
@RequestMapping("/info")
@Api(tags = "Info")
public class InfoController {

    @GetMapping
    public ResponseEntity getVersion()  {
        return new ResponseEntity("v-1.6.0", HttpStatus.OK);
    }

}
