package com.btpnsyariah.agendaku.fpb.other.service;

import com.btpnsyariah.agendaku.fpb.other.model.NationalHoliday;
import com.btpnsyariah.agendaku.fpb.other.model.NationalHolidayDTO;
import com.btpnsyariah.agendaku.fpb.other.repository.NationalHolidayRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class NationalHolidayServiceImpl implements NationalHolidayService {

    @Autowired
    NationalHolidayRepository nationalHolidayRepository;

    public List<NationalHolidayDTO> getAllNationalHolidaysStartingIn(Date startDate, Date endDate) {
        return nationalHolidayRepository.findAllNationalHolidaysStartingIn(startDate, endDate).stream().map(
                n -> NationalHolidayDTO.builder()
                        .id(n.getId())
                        .holidayName(n.getHolidayName())
                        .startDate(n.getStartDate())
                        .endDate(n.getEndDate())
                        .createdDate(n.getCreatedDate())
                        .createdBy(n.getCreatedBy())
                        .build()
        ).collect(Collectors.toList());
    }
}
