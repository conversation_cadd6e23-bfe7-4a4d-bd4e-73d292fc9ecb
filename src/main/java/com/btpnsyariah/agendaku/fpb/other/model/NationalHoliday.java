package com.btpnsyariah.agendaku.fpb.other.model;

import lombok.*;

import javax.persistence.*;
import java.sql.Timestamp;

@Entity
@Data
@Builder
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Table(name = "NationalHoliday", schema = "dbo")
public class NationalHoliday {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "Id", nullable = false, updatable = false)
    private Long id;

    @Column(name = "HolidayName")
    private String holidayName;

    @Column(name = "StartDate")
    private Timestamp startDate;

    @Column(name = "EndDate")
    private Timestamp endDate;

    @Column(name = "IsDeleted")
    private Boolean isDeleted;

    @Column(name = "CreatedDate")
    private Timestamp createdDate;

    @Column(name = "CreatedBy")
    private String createdBy;

    @Column(name = "LastUpdateBy")
    private String lastUpdateBy;

    @Column(name = "LastUpdateDate")
    private Timestamp lastUpdateDate;

    @PrePersist
    public void setDefaultValues(){
        if(isDeleted == null) isDeleted = false;
    }
}
