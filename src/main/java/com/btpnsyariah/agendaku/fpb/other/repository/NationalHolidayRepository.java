package com.btpnsyariah.agendaku.fpb.other.repository;

import com.btpnsyariah.agendaku.fpb.other.model.NationalHoliday;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface NationalHolidayRepository extends JpaRepository<NationalHoliday, Long> {
    @Query(nativeQuery = true, value = "SELECT * FROM NationalHoliday WHERE IsDeleted != 1")
    List<NationalHoliday> findAllNotDeleted();

    @Query(nativeQuery = true, value = "SELECT * FROM NationalHoliday WHERE CONVERT(DATE, StartDate) BETWEEN CONVERT(DATE, :startDate) AND CONVERT(DATE, :endDate)")
    List<NationalHoliday> findAllNationalHolidaysStartingIn(Date startDate, Date endDate);
}
