package com.btpnsyariah.agendaku.fpb.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.config.annotation.ViewControllerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;

@Configuration
public class SwaggerConfig {

    @Bean
    public Docket allDocket() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("all")
                .select().apis(RequestHandlerSelectors.withClassAnnotation(RestController.class))
                .build();
    }

    @Bean
    public Docket requisitionDocket() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("requisitions")
                .select().paths(path -> path.contains("requisitions") && !path.contains("items"))
                .build();
    }

    @Bean
    public Docket itemCataloguesDocket() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("item-catalogues")
                .select().paths(path -> path.contains("item-catalogues"))
                .build();
    }

    @Bean
    public Docket itemDocket() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("requisition-items")
                .select().paths(path -> path.contains("items"))
                .build();
    }

    @Bean
    public Docket invoiceDocket() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("invoices")
                .select().paths(path -> path.contains("invoices"))
                .build();
    }

    @Bean
    public WebMvcConfigurer webMvcConfigurer() {
        return new WebMvcConfigurer() {
            @Override
            public void addViewControllers(ViewControllerRegistry registry) {
                registry.addRedirectViewController("requisitions-swagger.ui.html", "swagger-ui/?urls.primaryName=requisitions");
                registry.addRedirectViewController("requisition-items-swagger.ui.html", "swagger-ui/?urls.primaryName=requisition-items");
                registry.addRedirectViewController("invoices-swagger.ui.html", "swagger-ui/?urls.primaryName=invoices");
                registry.addRedirectViewController("item-catalogues-swagger.ui.html", "swagger-ui/?urls.primaryName=item-catalogues");
                registry.addRedirectViewController("all-swagger.ui.html", "swagger-ui/?urls.primaryName=all");
            }
        };
    }

}
