package com.btpnsyariah.agendaku.fpb.config;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.util.ContentCachingRequestWrapper;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Enumeration;

import static java.nio.charset.StandardCharsets.UTF_8;

@Component
@Slf4j
public class ControllerInterceptor implements HandlerInterceptor {

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public boolean preHandle(final HttpServletRequest request,final HttpServletResponse response,final Object handler) throws Exception {
        String url = request.getRequestURL().append("?").append(request.getQueryString()).toString();
        log.info("get request for URL: {}", url);
        return true;
    }

    @Override
    public void postHandle(final HttpServletRequest request,final HttpServletResponse response,
                           final Object handler,final ModelAndView modelAndView) throws Exception {
    }

    @Override
    public void afterCompletion
            (final HttpServletRequest request, final HttpServletResponse response,final Object
                    handler,final Exception exception) throws Exception {

        ContentCachingRequestWrapper requestWrapper = new ContentCachingRequestWrapper(request);

        HttpHeaders requestHeaders = new HttpHeaders();
        Enumeration headerNames = requestWrapper.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = (String) headerNames.nextElement();
            requestHeaders.add(headerName, requestWrapper.getHeader(headerName));
        }
        String requestBody = IOUtils.toString(requestWrapper.getInputStream(),UTF_8);
        JsonNode requestJson = objectMapper.readTree(requestBody);

        String requestPath = request.getContextPath() + request.getServletPath();
        log.info("Response Code: {} : {}", requestPath, response.getStatus());
        log.info("Request URL: {} : {}", requestPath, request.getRequestURL());
        log.info("Request Header: {} : {}", requestPath, requestHeaders);
        log.info("Request Body: {} : {}", requestPath, requestJson);
    }
}
