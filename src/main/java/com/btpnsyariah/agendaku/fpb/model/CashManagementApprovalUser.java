package com.btpnsyariah.agendaku.fpb.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;
import java.util.Arrays;
import java.util.Objects;

public class CashManagementApprovalUser implements Serializable {

  private static final long serialVersionUID = -1L;

  @JsonProperty("username")
  private String nik;

  @JsonProperty("password")
  private char[] password;

  public CashManagementApprovalUser() {
  }

  public CashManagementApprovalUser(String nik, char[] password) {
    this.nik = nik;
    this.password = password;
  }

  public String getNik() {
    return nik;
  }

  public void setNik(String nik) {
    this.nik = nik;
  }

  public char[] getPassword() {
    return password;
  }

  public void setPassword(char[] password) {
    this.password = password;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    CashManagementApprovalUser user = (CashManagementApprovalUser) o;
    return Objects.equals(nik, user.nik) &&
        Arrays.equals(password, user.password);
  }

  @Override
  public int hashCode() {
    int result = Objects.hash(nik);
    result = 31 * result + Arrays.hashCode(password);
    return result;
  }
}
