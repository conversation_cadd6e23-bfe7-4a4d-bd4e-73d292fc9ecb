package com.btpnsyariah.agendaku.fpb.model;

public enum CashManagementTransactionStatus {

    NEW("NEW"), // Pengajuan dana
    DISBURSED_LB("DISBURSED_LB"), // Pengambilan dana dari lemari besi
    DISBURSED_KW("DISBURSED_KW"), // Pengembalian dana ke lemari besi
    DISTRIBUTED_KW("DISTRIBUTED_KW"), //Distribusi dana ke CO
    COLLECTED_KW("COLLECTED_KW"), // Pengambilan dana oleh CO
    COLLECTED_LB("COLLECTED_LB"), // Pemasukkan dana ke lemari besi
    COLLECTED_DO("COLLECTED_DO"), // Pengambilan dana oleh Dana Operational Wisma
    FPB_REIMBURSE("FPB_REIMBURSE"),
    FPB_SURPLUS("FPB_SURPLUS"),
    RC_COLLECTED_LB("REQUEST_CANCEL_COLLECTED_LB"), // RequestPemasukkan dana ke lemari besi
    RC_COLLECTED_DO("REQUEST_CANCEL_COLLECTED_DO"), // Request Pengambilan dana oleh Dana Operational Wisma
    CANCELLED("CANCELLED"); // Pengajuan dana ditolak

    private final String transactionStatus;

    CashManagementTransactionStatus(String transactionStatus) {
        this.transactionStatus = transactionStatus;
    }

    public String transactionStatus() {
        return transactionStatus;
    }
}
