package com.btpnsyariah.agendaku.fpb.model;

public class Constant {

    public static final String REQUISITION_UPLOAD_BA_RESET = "Upload BA atas transaksi overaging";

    private Constant() {
        throw new IllegalStateException("Constant class");
    }

    public static final String SETTLEMENT_REQUEST = "Submit penyelesaian";
    public static final String TRANSACTION_SETTLED = "Transaksi sudah di selesaikan";
    public static final String REVERSAL = "Reversal";
    public static final String REQUISITION_CANCELLATION = "Permintaan Pembatalan transaksi";
    public static final String REQUISITION_REVERSAL = "Koreksi transaksi";
    public static final String REVERSE_SETTLEMENT = "Pengembalian dana penyelesaian atas koreksi";
    public static final String REQUISITION_CANCEL_APPROVED_BM = "Pembatalan transaksi telah disetujui oleh BM";
    public static final String REQUISITION_CANCELLED = "Transaksi FPB telah dibatalkan";
    public static final String BA_FILENAME_PREFIX = "Agendaku_FPB_BA_";
}
