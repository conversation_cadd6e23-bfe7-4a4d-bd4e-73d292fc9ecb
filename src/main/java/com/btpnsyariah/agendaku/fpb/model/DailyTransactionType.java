package com.btpnsyariah.agendaku.fpb.model;

@SuppressWarnings({"WeakerAccess", "unused"})
public enum DailyTransactionType {
  PRS("PRS"),
  KW("Cash Out KW"),
  LIKUIDITAS("Likuiditas"),
  FPB("FPB");

  private final String dailyTransactionType;

  DailyTransactionType(String dailyTransactionType) {this.dailyTransactionType = dailyTransactionType;}

  public String dailyTransactionType() {
    return dailyTransactionType;
  }
}
