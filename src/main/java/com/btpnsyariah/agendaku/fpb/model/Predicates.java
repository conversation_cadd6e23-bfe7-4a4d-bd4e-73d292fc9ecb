package com.btpnsyariah.agendaku.fpb.model;

import com.btpnsyariah.agendaku.fpb.transaction.invoice.InvoiceEntity;
import com.btpnsyariah.agendaku.fpb.transaction.items.PaymentStatus;
import com.btpnsyariah.agendaku.fpb.transaction.items.RequisitionItemEntity;

import java.math.BigDecimal;
import java.util.function.Predicate;

public final class Predicates {

    private Predicates() {
        throw new IllegalStateException("Predicates class");
    }

    public static final Predicate<RequisitionItemEntity> PAYMENT_STATUS_CANCELLED = i -> i.getPaymentStatus() == PaymentStatus.CANCELLED;
    public static final Predicate<RequisitionItemEntity> PAYMENT_STATUS_IS_NOT_CANCELLED = i -> i.getPaymentStatus() != PaymentStatus.CANCELLED;
    public static final Predicate<InvoiceEntity> INVOICE_EXIST = r -> r.getInvoiceId() != null && !r.getInvoiceId().equalsIgnoreCase("");
    public static final Predicate<RequisitionItemEntity> PAYMENT_STATUS_ACTUALIZED = i -> (i.getPaymentStatus() == PaymentStatus.ACTUALIZED) &&
            i.getActualAmount() != null &&
            i.getActualAmount().compareTo(BigDecimal.ZERO) != 0 &&
            i.getRequisitionItemInvoices()
                    .stream()
                    .allMatch(INVOICE_EXIST);
    public static final Predicate<RequisitionItemEntity> PAYMENT_STATUS_IS_NOT_CREATED = i -> i.getPaymentStatus() != PaymentStatus.CREATED;
    public static final Predicate<InvoiceEntity> INVOICE_IS_DELETED = InvoiceEntity::isDeleted;
    public static final Predicate<InvoiceEntity> INVOICE_IS_NOT_DELETED = i -> !i.isDeleted();
}
