package com.btpnsyariah.agendaku.fpb.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Timestamp;

@SuppressWarnings({"WeakerAccess", "unused"})
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CoDailyTransactionRequest {

  private String coName;
  private BigDecimal requestAmount;
  private BigDecimal totalEstimatedPRSAmount;
  private BigDecimal totalEstimatedCollectedAmount;
  private BigInteger coDailyTransactionRequestID;
  private TransactionTypeCO transactionTypeCO;
  private CashManagementTransactionStatus transactionStatus;
  private BigInteger lbDailyTransactionRequestID;
  private String prsNotes;
  private String DailyTransactionType;
  private Integer hundreds;
  private Integer seventyFives;
  private Integer fiftys;
  private Integer twentys;
  private Integer tens;
  private Integer fives;
  private Integer smallMoney;
  private Timestamp date;
  private BigDecimal totalPrsPendingAmount;
  private BigDecimal totalPrsAmount;
  private BigDecimal otherTransaction;
  private BigDecimal liquidity;
  private BigDecimal totalCollectedAmount;
  private String otherTransactionNotes;
  private boolean isPiloting;

  public BigDecimal getLiquidity() {
    return liquidity;
  }

  public void setLiquidity(BigDecimal liquidity) {
    this.liquidity = liquidity;
  }

  public BigDecimal getTotalCollectedAmount() {
    return totalCollectedAmount;
  }

  public void setTotalCollectedAmount(BigDecimal totalCollectedAmount) {
    this.totalCollectedAmount = totalCollectedAmount;
  }

  public boolean isPiloting() {
    return isPiloting;
  }

  public void setPiloting(boolean piloting) {
    isPiloting = piloting;
  }

  public BigDecimal getOtherTransaction() { return otherTransaction; }

  public void setOtherTransaction(BigDecimal otherTransaction) { this.otherTransaction = otherTransaction; }

  public String getOtherTransactionNotes() { return otherTransactionNotes; }

  public void setOtherTransactionNotes(String otherTransactionNotes) { this.otherTransactionNotes = otherTransactionNotes; }

  public BigDecimal getTotalPrsPendingAmount() { return totalPrsPendingAmount; }

  public void setTotalPrsPendingAmount(BigDecimal totalPrsPendingAmount) { this.totalPrsPendingAmount = totalPrsPendingAmount; }

  public BigDecimal getTotalPrsAmount() { return totalPrsAmount; }

  public void setTotalPrsAmount(BigDecimal totalPrsAmount) { this.totalPrsAmount = totalPrsAmount; }

  public Integer getSeventyFives() {
    return seventyFives;
  }

  public void setSeventyFives(Integer seventyFives) {
    this.seventyFives = seventyFives;
  }

  public Timestamp getDate() {
    return date;
  }

  public void setDate(Timestamp date) {
    this.date = date;
  }

  public Integer getHundreds() {
    return hundreds;
  }

  public void setHundreds(Integer hundreds) {
    this.hundreds = hundreds;
  }

  public Integer getFiftys() {
    return fiftys;
  }

  public void setFiftys(Integer fiftys) {
    this.fiftys = fiftys;
  }

  public Integer getTwentys() {
    return twentys;
  }

  public void setTwentys(Integer twentys) {
    this.twentys = twentys;
  }

  public Integer getTens() {
    return tens;
  }

  public void setTens(Integer tens) {
    this.tens = tens;
  }

  public Integer getFives() {
    return fives;
  }

  public void setFives(Integer fives) {
    this.fives = fives;
  }

  public Integer getSmallMoney() {
    return smallMoney;
  }

  public void setSmallMoney(Integer smallMoney) {
    this.smallMoney = smallMoney;
  }

  public String getDailyTransactionType() {
    return DailyTransactionType;
  }

  public void setDailyTransactionType(String dailyTransactionType) {
    DailyTransactionType = dailyTransactionType;
  }

  public BigDecimal getTotalEstimatedCollectedAmount() {
    return totalEstimatedCollectedAmount;
  }

  public void setTotalEstimatedCollectedAmount(BigDecimal totalEstimatedCollectedAmount) {
    this.totalEstimatedCollectedAmount = totalEstimatedCollectedAmount;
  }

  public BigDecimal getTotalEstimatedPRSAmount() {
    return totalEstimatedPRSAmount;
  }

  public void setTotalEstimatedPRSAmount(BigDecimal totalEstimatedPRSAmount) {
    this.totalEstimatedPRSAmount = totalEstimatedPRSAmount;
  }

  public String getPrsNotes() {
    return prsNotes;
  }

  public void setPrsNotes(String prsNotes) {
    this.prsNotes = prsNotes;
  }

  public String getCoName() {
    return coName;
  }

  public void setCoName(String coName) {
    this.coName = coName;
  }

  public BigDecimal getRequestAmount() {
    return requestAmount;
  }

  public void setRequestAmount(BigDecimal requestAmount) {
    this.requestAmount = requestAmount;
  }

  public BigInteger getCoDailyTransactionRequestID() {
    return coDailyTransactionRequestID;
  }

  public void setCoDailyTransactionRequestID(BigInteger coDailyTransactionRequestID) {
    this.coDailyTransactionRequestID = coDailyTransactionRequestID;
  }

  public TransactionTypeCO getTransactionTypeCO() {
    return transactionTypeCO;
  }

  public void setTransactionTypeCO(TransactionTypeCO transactionTypeCO) {
    this.transactionTypeCO = transactionTypeCO;
  }

  public CashManagementTransactionStatus getTransactionStatus() {
    return transactionStatus;
  }

  public void setTransactionStatus(CashManagementTransactionStatus transactionStatus) {
    this.transactionStatus = transactionStatus;
  }

  public BigInteger getLbDailyTransactionRequestID() {
    return lbDailyTransactionRequestID;
  }

  public void setLbDailyTransactionRequestID(BigInteger lbDailyTransactionRequestID) {
    this.lbDailyTransactionRequestID = lbDailyTransactionRequestID;
  }

  @Override
  public String toString() {
    return "CoDailyTransactionRequest{" +
            "coName='" + coName + '\'' +
            ", requestAmount=" + requestAmount +
            ", totalEstimatedPRSAmount=" + totalEstimatedPRSAmount +
            ", totalEstimatedCollectedAmount=" + totalEstimatedCollectedAmount +
            ", coDailyTransactionRequestID=" + coDailyTransactionRequestID +
            ", transactionTypeCO=" + transactionTypeCO +
            ", transactionStatus=" + transactionStatus +
            ", lbDailyTransactionRequestID=" + lbDailyTransactionRequestID +
            ", prsNotes='" + prsNotes + '\'' +
            '}';
  }
}
