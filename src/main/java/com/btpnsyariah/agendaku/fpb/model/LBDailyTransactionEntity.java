package com.btpnsyariah.agendaku.fpb.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Objects;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@ToString
@Table(name = "LBDailyTransaction", schema = "dbo")
public class LBDailyTransactionEntity implements Serializable {

  private static final long serialVersionUID = 1L;

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id")
  private Long id;

  @Enumerated(EnumType.STRING)
  @Column(name = "TransactionType")
  private TransactionTypeLB transactionTypeLB;

  @Column(name = "MMSCode")
  private String mmsCode;

  @Enumerated(EnumType.STRING)
  @Column(name = "TransactionStatus")
  private CashManagementTransactionStatus transactionStatus;

  @Enumerated(EnumType.STRING)
  @Column(name = "RequestedType")
  private RequestedType requestedType;

  @Column(name = "TotalAmountRequest", columnDefinition = "DECIMAL(22,4) DEFAULT '0.0000'")
  private BigDecimal totalAmountRequest;

  @Column(name = "TotalAmount", columnDefinition = "DECIMAL(22,4) DEFAULT '0.0000'")
  private BigDecimal totalAmount;

  @Column(name = "Hundreds", columnDefinition = "INT DEFAULT '0'")
  private Integer hundreds;

  @Column(name = "SeventyFives", columnDefinition = "INT DEFAULT '0'")
  private Integer seventyFives;

  @Column(name = "Fiftys", columnDefinition = "INT DEFAULT '0'")
  private Integer fiftys;

  @Column(name = "Twentys", columnDefinition = "INT DEFAULT '0'")
  private Integer twentys;

  @Column(name = "Tens", columnDefinition = "INT DEFAULT '0'")
  private Integer tens;

  @Column(name = "Fives", columnDefinition = "INT DEFAULT '0'")
  private Integer fives;

  @Column(name = "SmallMoney", columnDefinition = "INT DEFAULT '0'")
  private Integer smallMoney;

  @Column(name = "Notes")
  private String notes;

  @Column(name = "CreatedDate")
  @JsonIgnoreProperties
  private Timestamp createdDate;

  @Column(name = "CreatedBy")
  @JsonIgnoreProperties
  private String createdBy;

  @Column(name = "UpdatedDate")
  @JsonIgnoreProperties
  private Timestamp updatedDate;

  @Column(name = "UpdatedBy")
  @JsonIgnoreProperties
  private String updatedBy;

  @Column(name = "ApprovedDate")
  @JsonIgnoreProperties
  private Timestamp approvedDate;

  @Column(name = "ApprovedBy")
  @JsonIgnoreProperties
  private String approvedBy;

  @Enumerated(EnumType.STRING)
  @Column(name = "DailyTransactionType")
  private DailyTransactionType dailyTransactionType;

  @Enumerated(EnumType.STRING)
  @Column(name = "Condition")
  private CashManagementTransactionCondition condition;

  @Enumerated(EnumType.STRING)
  @Column(name = "ProsperaPostingType")
  private CashManagementCoreBankingPostingType prosperaPostingType;

  @Column(name = "LBOpenHour")
  private String openHour;

  @Column(name = "LBCloseHour")
  private String closeHour;

  @Column(name = "IsPiloting")
  private boolean isPiloting;

  @Column(name = "IsReverseRequested")
  private boolean isReverseRequested;

  @Column(name = "ReverseNote")
  private String reverseNote;

  @PrePersist
  public void setToZeroIfNull() {
    if (totalAmount == null) {
      totalAmount = BigDecimal.ZERO;
    }
    if (hundreds == null) {
      hundreds = 0;
    }
    if(seventyFives == null){
      seventyFives = 0;
    }
    if (fiftys == null) {
      fiftys = 0;
    }
    if (twentys == null) {
      twentys = 0;
    }
    if (tens == null) {
      tens = 0;
    }
    if (fives == null) {
      fives = 0;
    }
    if (smallMoney == null) {
      smallMoney = 0;
    }
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (!(o instanceof LBDailyTransactionEntity)) return false;
    LBDailyTransactionEntity that = (LBDailyTransactionEntity) o;
    return Objects.equals(getCreatedDate(), that.createdDate) &&
            Objects.equals(getMmsCode(), that.mmsCode) &&
            Objects.equals(getTransactionStatus(), that.transactionStatus);
  }

  @Override
  public int hashCode() {
    return Objects.hash(getCreatedDate(), getMmsCode(), getTransactionStatus());
  }

}
