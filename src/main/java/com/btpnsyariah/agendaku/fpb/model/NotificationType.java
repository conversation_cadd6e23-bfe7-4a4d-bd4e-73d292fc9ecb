package com.btpnsyariah.agendaku.fpb.model;

public enum NotificationType {

    FPB_REQUEST("Permintaan FPB"),
    FPB_SETTLEMENT_REQUEST("Permintaan Penyelesaian FPB"),
    FPB_REQUEST_REJECTED_BM("Permintaan FPB telah ditolak oleh BM"),
    FPB_REQUEST_REJECTED_KW("Permintaan FPB telah ditolak oleh Kas Wisma"),
    FPB_REQUEST_APPROVED_BM("Permintaan FPB telah disetujui oleh BM"),
    FPB_REQUEST_APPROVED_KW("Permintaan FPB telah disetujui oleh Kas Wisma"),
    FPB_SETTLEMENT_REJECTED_BM("Penyelesaian FPB telah ditolak oleh BM"),
    FPB_SETTLEMENT_REJECTED_KW("Penyelesaian FPB telah ditolak oleh Kas Wisma"),
    FPB_SETTLEMENT_APPROVED_BM("Penyelesaian FPB telah disetujui oleh BM"),
    FPB_SETTLEMENT_APPROVED_KW("Penyelesaian FPB/SPM telah disetujui oleh BM dan Kas Wisma"),
    FPB_SETTLEMENT_APPROVED_KW_REIMBURSE("Penyelesaian FPB/SPM telah disetujui oleh BM dan Kas Wisma"),
    FPB_SETTLEMENT_APPROVED_KW_SURPLUS("Penyelesaian FPB/SPM telah disetujui oleh BM dan Kas Wisma"),
    FPB_REVERSAL_REQUEST("Permintaan koreksi FPB"),
    FPB_REVERSAL_APPROVED_BM("Permintaan koreksi FPB telah disetujui oleh BM"),
    FPB_REVERSAL_APPROVED_BM_REIMBURSE("Permintaan koreksi FPB telah disetujui oleh BM"),
    FPB_REVERSAL_APPROVED_BM_SURPLUS("Permintaan koreksi FPB telah disetujui oleh BM"),
    FPB_REVERSAL_REJECTED_BM("Permintaan koreksi FPB telah ditolak oleh BM"),
    FPB_CANCEL_REQUEST("Permintaan pembatalan transaksi FPB"),
    FPB_CANCEL_APPROVED_BM("Permintaan pembatalan transaksi FPB disetujui oleh BM"),
    FPB_CANCEL_REJECTED_BM("Permintaan pembatalan transaksi FPB ditolak oleh BM"),
    FPB_DOC_CREATED("Dokumen Pengajuan FPB & SPM sudah tersedia"),
    FPB_DOC_COMPLETED("Dokumen Penyelesaian FPB & SPM sudah tersedia"),
    FPB_REVERSAL_APPROVED_KFO("Permintaan koreksi FPB telah disetujui oleh BM");

    private final String notificationType;

    public String getNotificationType() {
        return notificationType;
    }

    NotificationType(String notificationType) {
        this.notificationType = notificationType;
    }
}
