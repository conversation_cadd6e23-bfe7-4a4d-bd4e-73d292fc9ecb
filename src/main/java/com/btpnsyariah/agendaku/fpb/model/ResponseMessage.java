package com.btpnsyariah.agendaku.fpb.model;

public final class ResponseMessage {
    public static final String REQUISITION_NOT_FOUND = "Permintaan FPB tidak ditemukan";
    public static final String CATALOGUE_NOT_FOUND = "Item katalog tidak ditemukan";
    public static final String REQUISITION_NOT_COMPLETED = "Permintaan FPB sebelumnya oleh %s dengan nomor FPB %s sejumlah Rp %s belum selesai";
    public static final String REQUISITION_MISSING_DATA = "Data permintaan FPB tidak lengkap, silahkan dicoba kembali";
    public static final String USER_NOT_FOUND = "User tidak ditemukan";
    public static final String ITEM_NOT_FOUND = "Barang tidak ditemukan";
    public static final String ITEM_OVERPRICE_LIMIT_20 = "Harga aktual barang melebihi limit 20%";
    public static final String USER_NOT_FOUND_ARG = "User dengan NIK %s tidak ditemukan";
    public static final String STATUS_NOT_MATCH = "Status transaksi tidak sesuai";
    public static final String SOMETHING_WENT_WRONG = "Maaf telah terjadi kesalahan";
    public static final String SOMETHING_WENT_WRONG_ARG = "Maaf telah terjadi kesalahan : %s";
    public static final String UNAUTHORIZED_ACCESS = "User tidak memiliki wewenang";
    public static final String INVALID_PARAM = "Permintaan tidak sesuai";
    public static final String BAD_REQUEST = "Permintaan tidak valid";
    public static final String INVALID_CANCEL_ITEM = "Pembatalan tidak dapat dilakukan apabila salah satu barang sudah terisi nominal aktual atau bukti pembayaran";
    public static final String INVOICE_UPLOADED = "Bukti pembayaran telah di unggah";
    public static final String BA_UPLOADED = "Berita acara telah di unggah";
    public static final String INVOICE_UPLOAD_FAIL = "Bukti pembayaran gagal di unggah : %s";
    public static final String BA_UPLOAD_FAIL = "Berita acara gagal di unggah : %s";
    public static final String BA_GET_FAIL = "Berita acara gagal di ambil : %s";
    public static final String INVOICE_FETCH_FAIL = "Bukti pembayaran gagal di ambil : %s";
    public static final String INVOICE_DELETED = "Bukti pembayaran telah dihapus";
    public static final String INVOICE_DELETE_FAIL = "Bukti pembayaran gagal dihapus : %s";
    public static final String REQUISITION_CANCELLED = "Permintaan pembatalan transaksi FPB telah di simpan";
    public static final String REQUISITION_REVERSED = "Permintaan koreksi transaksi FPB telah di simpan";
    public static final String REQUISITION_SETTLED = "Permintaan penyelesaian transaksi FPB telah di simpan";
    public static final String APPROVAL_SUBMITTED = "Persetujuan telah disimpan";
    public static final String REQUISITION_LIST_FETCHED = "Daftar transaksi FPB sukses di ambil";
    public static final String REQUISITION_BA_STATUS_FETCHED = "Requisition BA status fetched";
    public static final String REQUISITION_PIC_CHANGED = "Pemohon FPB sukses diubah";
    public static final String ERROR_WHILE_POSTING_LB_TRANSACTION = "Maaf telah terjadi kesalahan saat posting transaksi";
    public static final String INSUFFICIENT_DENOMINATION = "Denom tidak mencukupi";
    public static final String INSUFFICIENT_BALANCE = "Saldo tidak mencukupi";


    private ResponseMessage() {
        throw new IllegalStateException("ResponseMessage class");
    }
}
