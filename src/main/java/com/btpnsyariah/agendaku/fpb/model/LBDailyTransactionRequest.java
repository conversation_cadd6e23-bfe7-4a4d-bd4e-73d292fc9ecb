package com.btpnsyariah.agendaku.fpb.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.HashSet;
import java.util.Set;

@Builder
@Data
public class LBDailyTransactionRequest {

    private TransactionTypeCO transactionTypeCO;
    private String mmsCode;
    private String transactionStatus;
    private String requestedBy;
    private RequestedType requestedType;
    private TransactionTypeLB transactionType;
    private BigDecimal totalAmount;
    private BigDecimal totalAmountCORequest;
    private Integer hundreds;
    private Integer seventyFives;
    private Integer fiftys;
    private Integer twentys;
    private Integer tens;
    private Integer fives;
    private Integer smallMoney;
    private String notes;
    @JsonProperty("kasUser")
    private CashManagementApprovalUser user;
    private Set<CoDailyTransactionRequest> coDailyTransactionRequestSet = new HashSet<>();
    private String dataAnalytics;
    private DailyTransactionType dailyTransactionType;
    private Boolean journalSet;
    private String openHour;
    private String closeHour;

    @Override
    public String toString() {
        return "LBDailyTransactionRequestV1{" +
                "transactionTypeCO=" + transactionTypeCO +
                ", mmsCode='" + mmsCode + '\'' +
                ", transactionStatus='" + transactionStatus + '\'' +
                ", requestedBy='" + requestedBy + '\'' +
                ", requestedType=" + requestedType +
                ", transactionType=" + transactionType +
                ", totalAmount=" + totalAmount +
                ", totalAmountCORequest=" + totalAmountCORequest +
                ", hundreds=" + hundreds +
                ", seventyFives=" + seventyFives +
                ", fiftys=" + fiftys +
                ", twentys=" + twentys +
                ", tens=" + tens +
                ", fives=" + fives +
                ", smallMoney=" + smallMoney +
                ", notes='" + notes + '\'' +
                ", coDailyTransactionRequestSet=" + coDailyTransactionRequestSet +
                ", dataAnalytics='" + dataAnalytics + '\'' +
                ", dailyTransactionType=" + dailyTransactionType +
                ", journalSet=" + journalSet +
                '}';
    }
}
