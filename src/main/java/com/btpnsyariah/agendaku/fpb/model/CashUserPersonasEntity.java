package com.btpnsyariah.agendaku.fpb.model;


import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

@Entity
@IdClass(CashUserPersonasIdEntity.class)
@Table(name = "CashUserPersonas", schema = "dbo")
@AllArgsConstructor
@NoArgsConstructor
public class CashUserPersonasEntity implements Serializable {

    private static final long serialVersionUID = -1L;

    @Column(name = "MMSCode", updatable = false)
    private String mmsCode;

    @Column(name = "MMSName", updatable = false)
    private String mmsName;

    @Id
    @NotNull
    @Column(name = "NIK", updatable = false)
    private String nik;

    @Id
    @NotBlank
    @Column(name = "COCode", updatable = false)
    private String coCode;

    @Column(name = "COName", updatable = false)
    private String coName;

    @Column(name = "COMail", updatable = false)
    private String coMail;

    @Column(name = "PersonaRole")
    private String personaRole;

    @Column(name = "IsRoleSet")
    private String isRoleSet;

    @Column(name = "UpdatedDate")
    private Date updatedDate;

    @Column(name = "MMSCodeChild")
    private String mmsCodeChild;

    @Column(name = "MMSNameChild")
    private String mmsNameChild;

    @Column(name = "MobileId")
    private String mobileId;

    @Column(name = "FCMToken")
    private String fcmToken;

    @Column(name = "AccessToken")
    private String accessToken;

    @Column(name = "PilotingToggle")
    private Boolean pilotingToggle;

    public int getRoleId() {
        return roleId;
    }

    public void setRoleId(int roleId) {
        this.roleId = roleId;
    }

    @Column(name = "RoleId")
    private int roleId;



    public String getMobileId() {
        return mobileId;
    }

    public void setMobileId(String mobileId) {
        this.mobileId = mobileId;
    }

    public String getMmsCodeChild() {
        return mmsCodeChild;
    }

    public void setMmsCodeChild(String mmsCodeChild) {
        this.mmsCodeChild = mmsCodeChild;
    }

    public String getMmsNameChild() {
        return mmsNameChild;
    }

    public void setMmsNameChild(String mmsNameChild) {
        this.mmsNameChild = mmsNameChild;
    }

    public String getMmsCode() {
        return mmsCode;
    }

    public void setMmsCode(String mmsCode) {
        this.mmsCode = mmsCode;
    }

    public String getMmsName() {
        return mmsName;
    }

    public void setMmsName(String mmsName) {
        this.mmsName = mmsName;
    }

    public String getNik() {
        return nik;
    }

    public void setNik(String nik) {
        this.nik = nik;
    }

    public String getIsRoleSet() {
        return isRoleSet;
    }

    public void setIsRoleSet(String isRoleSet) {
        this.isRoleSet = isRoleSet;
    }

    public String getCoCode() {
        return coCode;
    }

    public void setCoCode(String coCode) {
        this.coCode = coCode;
    }

    public String getCoName() {
        return coName;
    }

    public void setCoName(String coName) {
        this.coName = coName;
    }

    public String getCoMail() {
        return coMail;
    }

    public void setCoMail(String coMail) {
        this.coMail = coMail;
    }

    public String getPersonaRole() {
        return personaRole;
    }

    public void setPersonaRole(String personaRole) {
        this.personaRole = personaRole;
    }

    public Date getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(Date updatedDate) {
        this.updatedDate = updatedDate;
    }

    public String getFcmToken() {
        return Objects.requireNonNullElse(fcmToken, "");
    }

    public void setFcmToken(String fcmToken) {
        this.fcmToken = fcmToken;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public Boolean getPilotingToggle() {
        return pilotingToggle;
    }

    public void setPilotingToggle(Boolean pilotingToggle) {
        this.pilotingToggle = pilotingToggle;
    }

    @Override
    public String toString() {
        return "CashUserPersonasEntity{" +
                "mmsCode='" + mmsCode + '\'' +
                ", mmsName='" + mmsName + '\'' +
                ", nik='" + nik + '\'' +
                ", coCode='" + coCode + '\'' +
                ", coName='" + coName + '\'' +
                ", personaRole='" + personaRole + '\'' +
                ", updatedDate=" + updatedDate +
                ", mmsCodeChild='" + mmsCodeChild + '\'' +
                ", mmsNameChild='" + mmsNameChild + '\'' +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof CashUserPersonasEntity)) return false;
        CashUserPersonasEntity cashUserPersona = (CashUserPersonasEntity) o;
        return Objects.equals(getNik(), cashUserPersona.getNik()) && Objects.equals(getCoCode(), cashUserPersona.getCoCode());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getNik()) + Objects.hash(getCoCode());
    }

    public CashUserPersonasEntity(String mmsCode, String mmsName, String nik, String coCode, String coName, String coMail, String personaRole) {
        this.mmsCode = mmsCode;
        this.mmsName = mmsName;
        this.nik = nik;
        this.coCode = coCode;
        this.coName = coName;
        this.coMail = coMail;
        this.personaRole = personaRole;
    }
}
