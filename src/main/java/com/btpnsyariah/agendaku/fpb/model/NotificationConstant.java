package com.btpnsyariah.agendaku.fpb.model;

public final class NotificationConstant {

    private NotificationConstant() {
        throw new IllegalStateException("Notification class");
    }

    public static final String APPROVAL_NOTIFICATION = "Halo %s, Ada %s yang masuk nih dari %s sejumlah Rp %s , selesaikan yuk!";
    public static final String APPROVAL_RESPONSE_NOTIFICATION = "Halo, %s %s";
    public static final String VCO_NOTIFICATION = "Halo %S, Berita Acara Cash Opname oleh KFO sudah tersedia, silahkan tap untuk melihat dokumen.";
    public static final String APPROVED = "APPROVED";
    public static final String REFUSED = "REFUSED";
    public static final String APPROVAL_REQUEST = "APPROVAL_REQUEST";
    public static final String APPROVAL_RESPONSE = "APPROVAL_RESPONSE";
    public static final String OTHER = "OTHER";
    public static final String SETTLEMENT_COMPLETED_BODY = "Penyelesaian FPB/SPM telah disetujui oleh BM dan Kas Wisma. Dokumen akan terkirim ke KFO secara otomatis";
    public static final String SETTLEMENT_REIMBURSE_BODY = "Penyelesaian FPB/SPM telah disetujui oleh BM dan Kas Wisma, silahkan hubungi PIC Lembes untuk proses mengambil kekurangan uang pada Menu Transaksi Lemari Besi. Dokumen akan terkirim ke KFO secara otomatis";
    public static final String SETTLEMENT_SURPLUS_BODY = "Penyelesaian FPB/SPM telah disetujui oleh BM dan Kas Wisma, silahkan hubungi PIC Lembes untuk proses pengembalian sisa uang pada Menu Transaksi Lemari Besi. Dokumen akan terkirim ke KFO secara otomatis";
    public static final String REVERSAL_COMPLETED_BODY = "Koreksi penyelesaian FPB/SPM telah disetujui oleh BM, silahkan koreksi biaya barang pada menu FPB";
    public static final String REVERSAL_REIMBURSE_BODY = "Koreksi penyelesaian FPB/SPM telah disetujui oleh BM, silahkan hubungi PIC Lembes untuk proses pengembalian dana penyelesaian pada Menu Transaksi Lemari Besi";
    public static final String REVERSAL_SURPLUS_BODY = "Koreksi penyelesaian FPB/SPM telah disetujui oleh BM, silahkan hubungi PIC Lembes untuk proses pengembalian dana penyelesaian Menu Transaksi Lemari Besi";
    public static final String FPB_DOC_CREATED = "Dokumen Pengajuan FPB & SPM oleh CO sudah tersedia, silahkan tap untuk melihat dokumen";
    public static final String FPB_DOC_COMPLETED = "Dokumen Penyelesaian FPB & SPM oleh CO sudah tersedia, silahkan tap untuk melihat dokumen";
    public static final String FPB_REVERSAL_APPROVED_KFO = "MMS %s telah melakukan koreksi pada FPB, yang dapat memengaruhi saldo KFO. Periksa detail di Monitoring FPB untuk tindak lanjut";


}
