package com.btpnsyariah.agendaku.fpb.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BaseResponse {
    private Boolean success;
    private String message;
    private Object data;
    private String code; //DDHHmmssSSS

    public BaseResponse(Boolean success, String message) {
        this.success = success;
        this.message = message;
    }

    public BaseResponse(Boolean success, String message, Object data) {
        this.success = success;
        this.message = message;
        this.data = data;
    }

    public BaseResponse(Boolean success, String message, String code) {
        this.success = success;
        this.message = message;
        this.code = code;
    }
}

