package com.btpnsyariah.agendaku.fpb.model;

import java.math.BigDecimal;

public interface LBDailyTransactionProjection {

  TransactionTypeLB getTransactionType();

  String getMmsCode();

  RequestedType getRequestedType();

  BigDecimal getTotalAmountRequest();

  BigDecimal getTotalAmount();

  Integer getHundreds();

  Integer getSeventyFives();

  Integer getFiftys();

  Integer getTwentys();

  Integer getTens();

  Integer getFives();

  Integer getSmallMoney();

  String getCreatedBy();

}
