package com.btpnsyariah.agendaku.fpb.model;

@SuppressWarnings({"WeakerAccess", "unused"})
public enum RequestedType {

  COVER_DANA("Menu Cover Dana"),
  CO_DAILY_TRANSACTION("Menu CO"),
  LB_DAILY_TRANSACTION("Menu Lemari <PERSON>"),
  KW_DAILY_TRANSACTION("Menu Kas Wisma"),
  DANA_OPERATIONAL("Menu Dana Operational"),
  DANA_OPERATIONAL_DETAIL("Menu Dana Operational Detail"),
  DANA_OPERATIONAL_DETAIL_TRANSACTION("Menu Dana Operational Detail Transaction"),
  REVERSE_TRANSACTION("Menu Reverse Transaction"),
  REVERSE_TRANSACTION_CD("Menu Reverse Cover Dana"),
  EXCHANGE_IN_TRANSACTION("Menu <PERSON> (Masuk)"),
  EXCHANGE_OUT_TRANSACTION("<PERSON><PERSON> (Keluar)"),
  CASH_OPNAME_TRANSACTION("Menu Cash Opname"),
  CASH_OPNAME_DIFF_TRANSACTION("Menu Selisih Cash Opname"),
  CASH_COUNT_TRANSACTION("Menu Cash Count"),
  CASH_COUNT_DIFF_TRANSACTION("Menu Selisih Cash Count"),
  OVERLIMIT_REQUEST("Menu warning overlimit"),
  OVERLIMIT_REPORT("overlimit report"),
  RESET_MMS("Menu Reset MMS"),
  RESET_BRW("Menu Reset BRW"),
  PROSPERA_MANUAL_JOURAL_LOG("PROSPERA_MANUAL_JOURAL_LOG"),
  FPB("FPB"),
  FPB_REVERSAL("FPB_REVERSAL"),
  FPB_ITEM_AOY("FPB_ITEM_AOY"),
  FPB_ITEM_BOY("FPB_ITEM_BOY"),
  FPB_DISBURSE("FPB_DISBURSE"),
  FPB_REIMBURSEMENT("FPB_REIMBURSEMENT"),
  FPB_COLLECTED_LB("FPB_COLLECTED_LB"),

  REVERSE_FPB_DISBURSE("REVERSE_FPB_DISBURSE"),
  REVERSE_FPB_REIMBURSEMENT("REVERSE_FPB_REIMBURSEMENT"),
  REVERSE_FPB_COLLECTED_LB("REVERSE_FPB_COLLECTED_LB"),
  REVERSE_FPB_ITEM_AOY("FPB_ITEM_AOY"),
  REVERSE_FPB_ITEM_BOY("FPB_ITEM_BOY"),

  CPC_SURVEY("CPC_SURVEY"),
  KFO_STATEMENT("KFO_STATEMENT"),

  OTHER("Menu Lainnya");


  private final String requestedBy;

  RequestedType(String requestedBy) {
    this.requestedBy = requestedBy;
  }

  public String requestedBy() {
    return requestedBy;
  }
}
