package com.btpnsyariah.agendaku.fpb.user;

import com.btpnsyariah.agendaku.fpb.exception.BusinessException;
import com.btpnsyariah.agendaku.fpb.model.CashUserPersonasEntity;
import com.btpnsyariah.agendaku.fpb.model.PersonaRole;
import com.btpnsyariah.agendaku.fpb.model.ResponseMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class CashUserPersonaImpl implements CashUserPersonaService {

    @Autowired
    CashUserPersonasRepository cashUserPersonasRepository;

    @Override
    public void validateUserRole(String nik, PersonaRole personaRole) throws BusinessException {
        CashUserPersonasEntity cashUserPersonasEntity = findByNik(nik);
        if(!cashUserPersonasEntity.getPersonaRole().equalsIgnoreCase(personaRole.getPersonaRole()) && !nik.equalsIgnoreCase(PersonaRole.ADMIN_QA.getPersonaRole()) && !nik.equalsIgnoreCase(PersonaRole.ADMIN_PO.getPersonaRole())) {
            throw new BusinessException(HttpStatus.BAD_REQUEST, ResponseMessage.UNAUTHORIZED_ACCESS);
        }
    }

    @Override
    public void validateUserRole(String nik, List<String> personaRoles) throws BusinessException {
        CashUserPersonasEntity cashUserPersonasEntity = findByNik(nik);
        if(!personaRoles.contains(cashUserPersonasEntity.getPersonaRole()) && !nik.equalsIgnoreCase(PersonaRole.ADMIN_QA.getPersonaRole()) && !nik.equalsIgnoreCase(PersonaRole.ADMIN_PO.getPersonaRole())) {
            throw new BusinessException(HttpStatus.BAD_REQUEST, ResponseMessage.UNAUTHORIZED_ACCESS);
        }
    }

    public CashUserPersonasEntity findByNik(String nik) throws BusinessException {
        return cashUserPersonasRepository.findByNik(nik).orElseThrow(() -> new BusinessException(HttpStatus.NOT_FOUND, String.format(ResponseMessage.USER_NOT_FOUND_ARG, nik)));
    }

    @Override
    public CashUserPersonasEntity findTop1PersonaByMmsAndRoleAndFcmNotNull(String mmsCode, String personaRole) throws BusinessException {
        return cashUserPersonasRepository.findTop1PersonaByMmsAndRoleAndFcmNotNull(mmsCode, personaRole)
                .orElseThrow(() -> new BusinessException(HttpStatus.NOT_FOUND, ResponseMessage.USER_NOT_FOUND));
    }

    @Override
    public CashUserPersonasEntity findTop1PersonaByMmsAndRole(String mmsCode, String personaRole) throws BusinessException {
        return cashUserPersonasRepository.findTop1PersonaByMmsAndRole(mmsCode, personaRole)
                .orElseThrow(() -> new BusinessException(HttpStatus.NOT_FOUND, ResponseMessage.USER_NOT_FOUND));
    }

    @Override
    public CashUserPersonasEntity findTop1PersonaByNikAndFcmTokenNotNull(String nik) throws BusinessException {
        return cashUserPersonasRepository.findTop1PersonaByNikAndFcmTokenNotNull(nik)
                .orElseThrow(() -> new BusinessException(HttpStatus.NOT_FOUND, String.format(ResponseMessage.USER_NOT_FOUND_ARG, nik)));
    }

    @Override
    public boolean validateUserPrivileges(String nik, String comparingNik) {
        return nik.equalsIgnoreCase(comparingNik) || nik.equalsIgnoreCase(PersonaRole.ADMIN_QA.getPersonaRole()) || nik.equalsIgnoreCase(PersonaRole.ADMIN_PO.getPersonaRole());
    }

    @Override
    public List<CashUserPersonasEntity> findByKfoPersonasByMmsCode(String mmsCode) {
        return cashUserPersonasRepository.findKfoPersonasByMmsCode(mmsCode);
    }

}
