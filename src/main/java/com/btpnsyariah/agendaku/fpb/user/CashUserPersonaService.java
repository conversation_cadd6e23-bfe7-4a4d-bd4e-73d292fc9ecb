package com.btpnsyariah.agendaku.fpb.user;

import com.btpnsyariah.agendaku.fpb.exception.BusinessException;
import com.btpnsyariah.agendaku.fpb.model.CashUserPersonasEntity;
import com.btpnsyariah.agendaku.fpb.model.PersonaRole;

import java.util.List;

public interface CashUserPersonaService {

    void validateUserRole(String nik, PersonaRole personaRole) throws BusinessException;

    void validateUserRole(String nik, List<String> personaRoles) throws BusinessException;

    CashUserPersonasEntity findByNik(String nik) throws BusinessException;

    CashUserPersonasEntity findTop1PersonaByMmsAndRoleAndFcmNotNull(String mmsCode, String personaRole) throws BusinessException;

    CashUserPersonasEntity findTop1PersonaByMmsAndRole(String mmsCode, String personaRole) throws BusinessException;

    CashUserPersonasEntity findTop1PersonaByNikAndFcmTokenNotNull(String nik) throws BusinessException;

    boolean validateUserPrivileges(String nik, String comparingNik);

    List<CashUserPersonasEntity> findByKfoPersonasByMmsCode(String mmsCode);
}
