package com.btpnsyariah.agendaku.fpb.user;

import com.btpnsyariah.agendaku.fpb.model.CashUserPersonasEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface CashUserPersonasRepository extends JpaRepository<CashUserPersonasEntity, Integer>, JpaSpecificationExecutor<CashUserPersonasEntity> {

    @Query(nativeQuery = true, value = "SELECT TOP 1 * FROM dbo.CashUserPersonas WHERE MMSCode = :mmsCode AND PersonaRole = :personaRole AND FCMToken IS NOT NULL ORDER BY UpdatedDate DESC")
    Optional<CashUserPersonasEntity> findTop1PersonaByMmsAndRoleAndFcmNotNull(@Param("mmsCode") String mmsCode, @Param("personaRole") String personaRole);

    @Query(nativeQuery = true, value = "SELECT TOP 1 * FROM dbo.CashUserPersonas WHERE MMSCode = :mmsCode AND PersonaRole = :personaRole ORDER BY UpdatedDate DESC")
    Optional<CashUserPersonasEntity> findTop1PersonaByMmsAndRole(@Param("mmsCode") String mmsCode, @Param("personaRole") String personaRole);

    @Query(nativeQuery = true, value = "SELECT TOP 1 * FROM dbo.CashUserPersonas WHERE NIK = :nik AND FCMToken IS NOT NULL ORDER BY UpdatedDate desc")
    Optional<CashUserPersonasEntity> findTop1PersonaByNikAndFcmTokenNotNull(@Param("nik") final String nik);

    @Query(nativeQuery = true, value = "SELECT TOP 1 * FROM dbo.CashUserPersonas WHERE NIK = :nik ORDER BY UpdatedDate desc")
    Optional<CashUserPersonasEntity> findByNik(String nik);

    @Query(nativeQuery = true, value = "SELECT * FROM CashUserPersonas cup WHERE cup.MMSCode = CONCAT('K', (SELECT TOP 1 KfoCode FROM CoverDanaMapping WHERE MMSCode = :mmsCode ORDER BY UpdatedDate DESC))")
    List<CashUserPersonasEntity> findKfoPersonasByMmsCode(@Param("mmsCode") String mmsCode);
}
