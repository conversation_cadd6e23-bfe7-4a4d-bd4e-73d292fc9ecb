package com.btpnsyariah.agendaku.fpb.service;

import com.btpnsyariah.agendaku.fpb.exception.BusinessException;
import com.btpnsyariah.agendaku.fpb.model.LBDailyTransactionRequest;
import com.btpnsyariah.agendaku.fpb.model.ResponseMessage;
import com.btpnsyariah.agendaku.fpb.util.Utility;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

@Component
@Slf4j
public class ExternalCommServiceImpl implements ExternalCommService {

    public static final String DEVICE_ID = "qe";
    @Autowired
    private RestTemplate restTemplate;

    @Value("${agendaku.cm.post.lb}")
    private String postLbTransactionUrl;

    @Override
    public ResponseEntity postLbTransaction(LBDailyTransactionRequest lbDailyTransactionRequest, String userId, String accessToken, String coCode, String rrn) throws BusinessException {

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("Access-Token", accessToken);
        headers.add("UserId", userId);
        headers.add("CoCode", coCode);
        headers.add("deviceId", DEVICE_ID);
        headers.add("RetrievalReferenceNumber", rrn);

        HttpEntity<LBDailyTransactionRequest> request = new HttpEntity<>(lbDailyTransactionRequest, headers);
        ResponseEntity<Object> response = null;

        try {
            log.info("[FPB External Comm Service] Posting LB transaction to \n{}\nwith value : {}\nAnd headers : {}", postLbTransactionUrl, lbDailyTransactionRequest, headers);

            response = restTemplate.postForEntity(
                    postLbTransactionUrl, request, Object.class);

            log.info("[FPB External Comm Service] LB transaction posted with result : {} | {}", response.getStatusCode(), response.getBody());
            return  response;
        } catch (HttpClientErrorException e) {
            log.info("[FPB External Comm Service] LB transaction fail to post with message : {} | {}", e.getMessage(), Utility.getStackTrace(e));
            if(e.getStatusCode().equals(HttpStatus.METHOD_FAILURE)) {
                throw new BusinessException(HttpStatus.INTERNAL_SERVER_ERROR, ResponseMessage.INSUFFICIENT_DENOMINATION);
            } else if (e.getStatusCode().equals(HttpStatus.PRECONDITION_FAILED)) {
                throw new BusinessException(HttpStatus.INTERNAL_SERVER_ERROR, ResponseMessage.INSUFFICIENT_BALANCE);
            } else {
                throw new BusinessException(HttpStatus.INTERNAL_SERVER_ERROR, ResponseMessage.SOMETHING_WENT_WRONG);
            }
        } catch (Exception e) {
            log.info("[FPB External Comm Service] LB transaction fail to post with message : {} | {}", e.getMessage(), Utility.getStackTrace(e));
            throw new BusinessException(HttpStatus.INTERNAL_SERVER_ERROR, e.getMessage());
        }
    }
}
