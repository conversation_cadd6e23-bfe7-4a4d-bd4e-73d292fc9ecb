package com.btpnsyariah.agendaku.fpb.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.concurrent.ListenableFutureCallback;

@Component
public class KafkaProducerServiceImpl implements KafkaProducerService{

    private final Logger log = LoggerFactory.getLogger(KafkaProducerServiceImpl.class);

    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;

    @Override
    public void sendMessage(String topic, String message) {
        log.info("Sending to topic {} message {}", topic, message);
        kafkaTemplate.send(topic, message)
                .addCallback(new ListenableFutureCallback() {
                    @Override
                    public void onSuccess(Object result) {
                        log.error("Success to send message");
                    }

                    @Override
                    public void onFailure(Throwable throwable) {
                        log.error("Fail to send message", throwable);
                    }
                });
    }
}
