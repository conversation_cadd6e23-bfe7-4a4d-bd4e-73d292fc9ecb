package com.btpnsyariah.agendaku.fpb.service;

import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

public interface StorageService {

    ResponseEntity send(
            MultipartFile file,
            String token,
            String fileName,
            HttpMethod requestType,
            MediaType mediaType) throws IOException;

    ResponseEntity send(
            String token,
            String fileName,
            HttpMethod requestType);

}
