package com.btpnsyariah.agendaku.fpb.service;

import com.btpnsyariah.agendaku.fpb.model.*;
import com.btpnsyariah.agendaku.fpb.user.CashUserPersonaService;
import com.btpnsyariah.agendaku.fpb.util.Utility;
import com.google.gson.Gson;
import org.apache.commons.text.WordUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class NotificationConfiguratorImpl implements NotificationConfigurator{

    private static final Logger LOGGER = LoggerFactory.getLogger(NotificationConfiguratorImpl.class);

    private static final String LOG_ERROR = "[NotificationConfiguratorService] There was an error while building notification : {} | {}";

    @Value("${agendaku.kafka.topic.notification}")
    private String notificationTopic;

    @Autowired
    private CashUserPersonaService cashUserPersonaService;

    @Autowired
    private KafkaProducerService kafkaProducerService;

    List<NotificationType> bmRequestNotificationTypes = new ArrayList<>(List.of(
            NotificationType.FPB_REQUEST,
            NotificationType.FPB_SETTLEMENT_REQUEST,
            NotificationType.FPB_REVERSAL_REQUEST,
            NotificationType.FPB_CANCEL_REQUEST
    ));

    List<NotificationType> kwRequestNotificationTypes = new ArrayList<>(List.of(
            NotificationType.FPB_REQUEST_APPROVED_BM,
            NotificationType.FPB_SETTLEMENT_APPROVED_BM
    ));

    @Override
    public void pushApprovalRequestNotification(
            String mmsCode,
            String createdBy,
            String amount,
            NotificationType notificationType) {

        try {
            if(bmRequestNotificationTypes.contains(notificationType)) {
                pushNotification(
                        setNotification(
                                PersonaRole.BM.getPersonaRole(),
                                NotificationConstant.APPROVAL_REQUEST,
                                cashUserPersonaService.findTop1PersonaByMmsAndRoleAndFcmNotNull(mmsCode, PersonaRole.BM.getPersonaRole()),
                                cashUserPersonaService.findTop1PersonaByNikAndFcmTokenNotNull(createdBy),
                                notificationType,
                                amount));
            } else if(kwRequestNotificationTypes.contains(notificationType)) {
                pushNotification(
                        setNotification(
                                PersonaRole.KW.getPersonaRole(), 
                                NotificationConstant.APPROVAL_REQUEST,
                                cashUserPersonaService.findTop1PersonaByMmsAndRoleAndFcmNotNull(mmsCode, PersonaRole.KW.getPersonaRole()),
                                cashUserPersonaService.findTop1PersonaByNikAndFcmTokenNotNull(createdBy),
                                notificationType,
                                amount));
            } else {
                throw new IllegalArgumentException(ResponseMessage.INVALID_PARAM);
            }
        } catch (Exception e) {
            LOGGER.error(LOG_ERROR, e.getMessage(), Utility.getStackTrace(e));
        }
    }

    @Override
    public void pushApprovalResponseNotification(String createdBy, NotificationType notificationType){
        try {
            CashUserPersonasEntity cashUserPersonasEntity = cashUserPersonaService.findTop1PersonaByNikAndFcmTokenNotNull(createdBy);

            pushNotification(
                    setNotification(
                            cashUserPersonasEntity.getPersonaRole(),
                            NotificationConstant.APPROVAL_RESPONSE,
                            cashUserPersonasEntity,
                            null,
                            notificationType,
                            null));
        } catch (Exception e) {
            LOGGER.error(LOG_ERROR, e.getMessage(), Utility.getStackTrace(e));
        }

    }

    @Override
    public void pushKfoDocNotification(String mmsCode, NotificationType notificationType) {
        try {
            List<CashUserPersonasEntity> kfoPersonas = cashUserPersonaService.findByKfoPersonasByMmsCode(mmsCode);

            kfoPersonas.forEach(c -> pushNotification(
                    setNotification(
                            c.getPersonaRole(),
                            NotificationConstant.APPROVAL_RESPONSE,
                            c,
                            null,
                            notificationType,
                            null))
            );
        } catch (Exception e) {
            LOGGER.error(LOG_ERROR, e.getMessage(), Utility.getStackTrace(e));
        }
    }

    private String setNotification(
            String personaRole,
            String notification,
            CashUserPersonasEntity receiver,
            CashUserPersonasEntity sender,
            NotificationType notificationType,
            String amount) {
        return new Gson().toJson(
                new PushNotificationRequest(
                        receiver.getNik(),
                        personaRole,
                        receiver.getFcmToken(),
                        receiver.getMmsCode(),
                        receiver.getMmsName(),
                        notificationType.getNotificationType(),
                        notificationBodyBuilder(
                                notification,
                                notificationType,
                                receiver,
                                sender,
                                amount
                        ),
                        PersonaRole.KFO.getPersonaRole().equalsIgnoreCase(personaRole) ? "KFO" : "MMS",
                        notificationType.toString().toLowerCase()
                )
        );
    }

    private String notificationBodyBuilder(
            String notification,
            NotificationType notificationType,
            CashUserPersonasEntity receiver,
            CashUserPersonasEntity sender,
            String amount) {
        if(notification.equalsIgnoreCase(NotificationConstant.APPROVAL_REQUEST)) {
            return String.format(NotificationConstant.APPROVAL_NOTIFICATION,
                    WordUtils.capitalizeFully(receiver.getCoName()).replace("(h)","").replace("(H)",""),
                    notificationType.getNotificationType(),
                    WordUtils.capitalizeFully(sender.getCoName()),
                    amount);
        } else if(notification.equalsIgnoreCase(NotificationConstant.APPROVAL_RESPONSE)) {
            switch (notificationType) {
                case FPB_SETTLEMENT_APPROVED_KW:
                    return String.format(NotificationConstant.APPROVAL_RESPONSE_NOTIFICATION,
                            WordUtils.capitalizeFully(receiver.getCoName()),
                            NotificationConstant.SETTLEMENT_COMPLETED_BODY);
                case FPB_SETTLEMENT_APPROVED_KW_REIMBURSE:
                    return String.format(NotificationConstant.APPROVAL_RESPONSE_NOTIFICATION,
                            WordUtils.capitalizeFully(receiver.getCoName()),
                            NotificationConstant.SETTLEMENT_REIMBURSE_BODY);
                case FPB_SETTLEMENT_APPROVED_KW_SURPLUS:
                    return String.format(NotificationConstant.APPROVAL_RESPONSE_NOTIFICATION,
                            WordUtils.capitalizeFully(receiver.getCoName()),
                            NotificationConstant.SETTLEMENT_SURPLUS_BODY);
                case FPB_REVERSAL_APPROVED_BM:
                    return String.format(NotificationConstant.APPROVAL_RESPONSE_NOTIFICATION,
                        WordUtils.capitalizeFully(receiver.getCoName()),
                        NotificationConstant.REVERSAL_COMPLETED_BODY);
                case FPB_REVERSAL_APPROVED_BM_REIMBURSE:
                    return String.format(NotificationConstant.APPROVAL_RESPONSE_NOTIFICATION,
                            WordUtils.capitalizeFully(receiver.getCoName()),
                            NotificationConstant.REVERSAL_REIMBURSE_BODY);
                case FPB_REVERSAL_APPROVED_BM_SURPLUS:
                    return String.format(NotificationConstant.APPROVAL_RESPONSE_NOTIFICATION,
                            WordUtils.capitalizeFully(receiver.getCoName()),
                            NotificationConstant.REVERSAL_SURPLUS_BODY);
                case FPB_DOC_CREATED:
                    return String.format(NotificationConstant.APPROVAL_RESPONSE_NOTIFICATION,
                            WordUtils.capitalizeFully(receiver.getCoName()),
                            NotificationConstant.FPB_DOC_CREATED);
                case FPB_DOC_COMPLETED:
                    return String.format(NotificationConstant.APPROVAL_RESPONSE_NOTIFICATION,
                            WordUtils.capitalizeFully(receiver.getCoName()),
                            NotificationConstant.FPB_DOC_COMPLETED);
                case FPB_REVERSAL_APPROVED_KFO:
                    return String.format(NotificationConstant.FPB_REVERSAL_APPROVED_KFO, WordUtils.capitalizeFully(receiver.getMmsCode()));
                default:
                    return String.format(NotificationConstant.APPROVAL_RESPONSE_NOTIFICATION,
                            WordUtils.capitalizeFully(receiver.getCoName()),
                            notificationType.getNotificationType());
            }
        } else {
            throw new IllegalArgumentException(ResponseMessage.INVALID_PARAM);
        }
    }

    private void pushNotification(String notification) {
        kafkaProducerService.sendMessage(notificationTopic, notification);
    }
}
