package com.btpnsyariah.agendaku.fpb.service;

import com.btpnsyariah.agendaku.fpb.exception.BusinessException;
import com.btpnsyariah.agendaku.fpb.model.LBDailyTransactionRequest;
import org.springframework.http.ResponseEntity;

public interface ExternalCommService {

    ResponseEntity postLbTransaction(LBDailyTransactionRequest lbDailyTransactionRequest, String userId, String accessToken, String coCode, String rrn) throws BusinessException;

}
