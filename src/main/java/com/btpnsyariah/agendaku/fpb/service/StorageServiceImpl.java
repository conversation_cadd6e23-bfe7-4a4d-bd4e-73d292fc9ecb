package com.btpnsyariah.agendaku.fpb.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.print.attribute.standard.Media;
import java.io.IOException;

@Component
public class StorageServiceImpl implements StorageService {

    @Autowired
    private RestTemplate restTemplate;

    @Value("${storage.path}")
    private String storagePath;

    private final String COMM_LOG = "[StorageService] Communicating to storage : {}{}";

    private static final Logger LOGGER = LoggerFactory.getLogger(StorageServiceImpl.class);

    @Override
    public ResponseEntity send(MultipartFile file,
                               String token,
                               String fileName,
                               HttpMethod requestType,
                               MediaType mediaType) throws IOException {
        HttpHeaders headers = new HttpHeaders();
        headers.add("Authorization", "Bearer " + token);
        headers.setContentType(mediaType);

        LOGGER.info(COMM_LOG, storagePath, fileName);
        return restTemplate.exchange(storagePath + fileName, requestType, new HttpEntity<>(file.getBytes(), headers), Media.class);
    }

    @Override
    public ResponseEntity send(String token, String fileName, HttpMethod requestType) {
        HttpHeaders headers = new HttpHeaders();
        headers.add("Authorization", "Bearer " + token);

        if (requestType == HttpMethod.GET) {
            LOGGER.info(COMM_LOG, fileName);
            return restTemplate.exchange(storagePath + fileName, requestType, new HttpEntity<>(headers), byte[].class);
        } else {
            LOGGER.info(COMM_LOG, storagePath, fileName);
            return restTemplate.exchange(storagePath + fileName, requestType, new HttpEntity<>(headers), String.class);
        }
    }



}
