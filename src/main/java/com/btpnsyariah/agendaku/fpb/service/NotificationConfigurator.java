package com.btpnsyariah.agendaku.fpb.service;

import com.btpnsyariah.agendaku.fpb.model.NotificationType;

public interface NotificationConfigurator {

    void pushApprovalRequestNotification(
            String mmsCode,
            String createdBy,
            String amount,
            NotificationType notificationType
    );

    void pushApprovalResponseNotification(
            String createdBy,
            NotificationType notificationType
    );

    void pushKfoDocNotification(
            String mmsCode,
            NotificationType notificationType
    );

}
