package com.btpnsyariah.agendaku.fpb.transaction.requisition;

import com.btpnsyariah.agendaku.fpb.model.LBDailyTransactionEntity;
import com.btpnsyariah.agendaku.fpb.model.LBDailyTransactionProjection;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

@Repository
public interface RequisitionRepository extends JpaRepository<RequisitionEntity, Long> {
    @Query(nativeQuery = true, value = "SELECT\n" +
            "\tfpbr.Id,\n" +
            "\tCONVERT(VARCHAR(3),\n" +
            "\tROW_NUMBER() OVER(PARTITION BY LEFT(CONVERT(VARCHAR(10),\n" +
            "\tDATEADD(HOUR,7,fpbr.CreatedDate),\n" +
            "\t112),\n" +
            "\t6)\n" +
            "ORDER BY\n" +
            "\tfpbr.Id)) + '/' + fpbr.MMSCode + '/' + \n" +
            "FORMAT(DATEADD(HOUR,7,fpbr.CreatedDate),\n" +
            "\t'M/yyyy') AS RefNo,\n" +
            "\tDATEADD(HOUR,7,fpbr.CreatedDate) AS CreatedDate,\n" +
            "\tfpbr.CreatedBy AS CreatedByNik,\n" +
            "\t(\n" +
            "\tSELECT\n" +
            "\t\tTOP 1 cup.COName\n" +
            "\tFROM\n" +
            "\t\tCashUserPersonas cup\n" +
            "\tWHERE\n" +
            "\t\tcup.NIK = fpbr.CreatedBy) AS CreatedByName, \n" +
            "\tfpbr.RequestAmount,\n" +
            "\tfpbr.RequestStatus,\n" +
            "\tcase when fpbr.RequestStatus in ('REQUEST_REJECTED_BM','REQUEST_REJECTED_PIC_KAS','SETTLEMENT_REJECTED_BM','SETTLEMENT_REJECTED_PIC_KAS','RV_REQUESTED','CANCEL_REJECTED_BM') " +
            "then (select top 1 log.Note from FPBTransactionLog log where log.RequisitionId = fpbr.id order by CreatedDate desc) " +
            "else '' end as Note, " +
            "\tfpbr.ActualizedAmount\n" +
            "FROM\n" +
            "\tFPBRequest fpbr\n" +
            "WHERE\n" +
            "\tfpbr.MMSCode = :mmsCode\n" +
            "ORDER BY\n" +
            "\tfpbr.CreatedDate DESC")
    List<RequisitionListProjection> findByMmsCode(@Param("mmsCode") String mmsCode);

    @Query(nativeQuery = true, value = "SELECT\n" +
            "\tfpbr.Id,\n" +
            "\tcase when fpbr.RequestStatus in ('COMPLETED','COLLECTED_LB','REIMBURSEMENT') " +
            "then (select top 1 DATEADD(HOUR,7,log.CreatedDate) from FPBTransactionLog log where log.RequisitionId = fpbr.id " +
            "AND log.TransactionStatus = 'SETTLEMENT_APPROVED_PIC_KAS' order by CreatedDate desc) " +
            "else null end as SettlementDate, " +
            "\tfpbr.RequisitionNo AS RefNo,\n" +
            "\tDATEADD(HOUR,7,fpbr.CreatedDate) AS CreatedDate,\n" +
            "\tfpbr.CreatedBy AS CreatedByNik,\n" +
            "\t(\n" +
            "\tSELECT\n" +
            "\t\tTOP 1 cup.COName\n" +
            "\tFROM\n" +
            "\t\tCashUserPersonas cup\n" +
            "\tWHERE\n" +
            "\t\tcup.NIK = fpbr.CreatedBy) AS CreatedByName, \n" +
            "\tfpbr.RequestAmount,\n" +
            "\tfpbr.RequestStatus,\n" +
            "\tcase when fpbr.RequestStatus in ('REQUEST_REJECTED_BM','REQUEST_REJECTED_PIC_KAS','SETTLEMENT_REJECTED_BM','SETTLEMENT_REJECTED_PIC_KAS','RV_REQUESTED','CANCEL_REJECTED_BM') " +
            "then (select top 1 log.Note from FPBTransactionLog log where log.RequisitionId = fpbr.id order by CreatedDate desc) " +
            "else '' end as Note, " +
            "\tCASE\n" +
            "\t\tWHEN fpbr.RequestStatus = 'CANCEL_REJECTED_BM' THEN (\n" +
            "\t\tSELECT\n" +
            "\t\t\tTOP 1 log.TransactionStatus\n" +
            "\t\tFROM\n" +
            "\t\t\t(\n" +
            "\t\t\tSELECT\n" +
            "\t\t\t\tTOP 3 *\n" +
            "\t\t\tFROM\n" +
            "\t\t\t\tFPBTransactionLog fl\n" +
            "\t\t\tWHERE\n" +
            "\t\t\t\tfl.RequisitionId = fpbr.Id\n" +
            "\t\t\tORDER BY\n" +
            "\t\t\t\tfl.CreatedDate DESC) log\n" +
            "\t\tWHERE\n" +
            "\t\t\tlog.RequisitionId = fpbr.id\n" +
            "\t\tORDER BY\n" +
            "\t\t\tCreatedDate ASC)\n" +
            "\t\tWHEN fpbr.RequestStatus = 'CANCEL_REQUESTED' THEN (\n" +
            "\t\tSELECT\n" +
            "\t\t\tTOP 1 log.TransactionStatus\n" +
            "\t\tFROM\n" +
            "\t\t\t(\n" +
            "\t\t\tSELECT\n" +
            "\t\t\t\tTOP 2 *\n" +
            "\t\t\tFROM\n" +
            "\t\t\t\tFPBTransactionLog fl\n" +
            "\t\t\tWHERE\n" +
            "\t\t\t\tfl.RequisitionId = fpbr.Id\n" +
            "\t\t\tORDER BY\n" +
            "\t\t\t\tfl.CreatedDate DESC) log\n" +
            "\t\tWHERE\n" +
            "\t\t\tlog.RequisitionId = fpbr.id\n" +
            "\t\tORDER BY\n" +
            "\t\t\tCreatedDate ASC)" +
            "\t\tWHEN fpbr.RequestStatus = 'RV_REQUESTED' THEN (\n" +
            "\t\tSELECT\n" +
            "\t\t\tTOP 1 log.TransactionStatus\n" +
            "\t\tFROM\n" +
            "\t\t\t(\n" +
            "\t\t\tSELECT\n" +
            "\t\t\t\tTOP 2 *\n" +
            "\t\t\tFROM\n" +
            "\t\t\t\tFPBTransactionLog fl\n" +
            "\t\t\tWHERE\n" +
            "\t\t\t\tfl.RequisitionId = fpbr.Id\n" +
            "\t\t\tORDER BY\n" +
            "\t\t\t\tfl.CreatedDate DESC) log\n" +
            "\t\tWHERE\n" +
            "\t\t\tlog.RequisitionId = fpbr.id\n" +
            "\t\tORDER BY\n" +
            "\t\t\tCreatedDate ASC)" +
            "\t\tELSE ''\n" +
            "\tEND AS PreviousStatus,\n" +
            "\tfpbr.ActualizedAmount,\n" +
            "\t(\n" +
            "\tSELECT\n" +
            "\t\tTOP 1 fl.CreatedDate\n" +
            "\tFROM\n" +
            "\t\tFPBTransactionLog fl\n" +
            "\tWHERE\n" +
            "\t\tfl.RequisitionId = fpbr.Id\n" +
            "\t\tAND fl.TransactionStatus = 'DISBURSED_LB') AS FundDisbursedDate,\n" +
            "\tCASE\n" +
            "\t\tWHEN EXISTS(\n" +
            "\t\tSELECT\n" +
            "\t\t\tTOP 1 *\n" +
            "\t\tFROM\n" +
            "\t\t\tDocumentReport dr\n" +
            "\t\tWHERE\n" +
            "\t\t\tdr.TransactionId = fpbr.Id\n" +
            "\t\t\tAND dr.DocumentType = 'FPB_BA') THEN CAST (1 AS BIT)\n" +
            "\t\tELSE CAST(0 AS BIT)\n" +
            "\tEND BaExist\n" +
            "FROM\n" +
            "\tFPBRequest fpbr\n" +
            "WHERE\n" +
            "\tfpbr.MMSCode = :mmsCode\n" +
            "\tAND (DATEADD(HOUR, 7, fpbr.CreatedDate) > DATEADD(MONTH, -2, DATEADD(HOUR, 7 , GETDATE()))\n" +
            "\t\tOR (DATEADD(HOUR, 7, fpbr.CreatedDate) < DATEADD(MONTH, -2, DATEADD(HOUR, 7 , GETDATE()))\n" +
            "\t\t\tAND fpbr.RequestStatus NOT IN ('CANCELLED', 'COMPLETED')))\n" +
            "ORDER BY\n" +
            "\tfpbr.CreatedDate DESC")
    List<RequisitionListProjection> findByMmsCodeForList(@Param("mmsCode") String mmsCode);

    @Query(nativeQuery = true, value = "SELECT\n" +
            "\tfpbr.Id,\n" +
            "\tcase when fpbr.RequestStatus in ('COMPLETED','COLLECTED_LB','REIMBURSEMENT') " +
            "then (select top 1 DATEADD(HOUR,7,log.CreatedDate) from FPBTransactionLog log where log.RequisitionId = fpbr.id " +
            "AND log.TransactionStatus = 'SETTLEMENT_APPROVED_PIC_KAS' order by CreatedDate desc) " +
            "else null end as SettlementDate, " +
            "\tfpbr.RequisitionNo AS RefNo,\n" +
            "\tDATEADD(HOUR,\n" +
            "        7,\n" +
            "        fpbr.CreatedDate) AS CreatedDate,\n" +
            "\tfpbr.CreatedBy AS CreatedByNik,\n" +
            "\t(\n" +
            "\tSELECT\n" +
            "\t\tTOP 1 cup.COName\n" +
            "\tFROM\n" +
            "\t\tCashUserPersonas cup\n" +
            "\tWHERE\n" +
            "\t\tcup.NIK = fpbr.CreatedBy) AS CreatedByName,\n" +
            "\tfpbr.RequestAmount,\n" +
            "\tfpbr.RequestStatus,\n" +
            "\tCASE\n" +
            "\t\tWHEN fpbr.RequestStatus IN ('REQUEST_REJECTED_BM',\n" +
            "            'REQUEST_REJECTED_PIC_KAS',\n" +
            "            'SETTLEMENT_REJECTED_BM',\n" +
            "            'SETTLEMENT_REJECTED_PIC_KAS',\n" +
            "            'RV_REQUESTED',\n" +
            "            'CANCEL_REJECTED_BM') THEN (\n" +
            "\t\tSELECT\n" +
            "\t\t\tTOP 1 log.Note\n" +
            "\t\tFROM\n" +
            "\t\t\tFPBTransactionLog log\n" +
            "\t\tWHERE\n" +
            "\t\t\tlog.RequisitionId = fpbr.id\n" +
            "\t\tORDER BY\n" +
            "\t\t\tCreatedDate DESC)\n" +
            "\t\tELSE ''\n" +
            "\tEND AS Note,\n" +
            "\tCASE\n" +
            "\t\tWHEN fpbr.RequestStatus = 'CANCEL_REJECTED_BM' THEN (\n" +
            "\t\tSELECT\n" +
            "\t\t\tTOP 1 log.TransactionStatus\n" +
            "\t\tFROM\n" +
            "\t\t\t(\n" +
            "\t\t\tSELECT\n" +
            "\t\t\t\tTOP 3 *\n" +
            "\t\t\tFROM\n" +
            "\t\t\t\tFPBTransactionLog fl\n" +
            "\t\t\tWHERE\n" +
            "\t\t\t\tfl.RequisitionId = fpbr.Id\n" +
            "\t\t\tORDER BY\n" +
            "\t\t\t\tfl.CreatedDate DESC) log\n" +
            "\t\tWHERE\n" +
            "\t\t\tlog.RequisitionId = fpbr.id\n" +
            "\t\tORDER BY\n" +
            "\t\t\tCreatedDate ASC)\n" +
            "\t\tWHEN fpbr.RequestStatus = 'CANCEL_REQUESTED' THEN (\n" +
            "\t\tSELECT\n" +
            "\t\t\tTOP 1 log.TransactionStatus\n" +
            "\t\tFROM\n" +
            "\t\t\t(\n" +
            "\t\t\tSELECT\n" +
            "\t\t\t\tTOP 2 *\n" +
            "\t\t\tFROM\n" +
            "\t\t\t\tFPBTransactionLog fl\n" +
            "\t\t\tWHERE\n" +
            "\t\t\t\tfl.RequisitionId = fpbr.Id\n" +
            "\t\t\tORDER BY\n" +
            "\t\t\t\tfl.CreatedDate DESC) log\n" +
            "\t\tWHERE\n" +
            "\t\t\tlog.RequisitionId = fpbr.id\n" +
            "\t\tORDER BY\n" +
            "\t\t\tCreatedDate ASC)" +
            "\t\tWHEN fpbr.RequestStatus = 'RV_REQUESTED' THEN (\n" +
            "\t\tSELECT\n" +
            "\t\t\tTOP 1 log.TransactionStatus\n" +
            "\t\tFROM\n" +
            "\t\t\t(\n" +
            "\t\t\tSELECT\n" +
            "\t\t\t\tTOP 2 *\n" +
            "\t\t\tFROM\n" +
            "\t\t\t\tFPBTransactionLog fl\n" +
            "\t\t\tWHERE\n" +
            "\t\t\t\tfl.RequisitionId = fpbr.Id\n" +
            "\t\t\tORDER BY\n" +
            "\t\t\t\tfl.CreatedDate DESC) log\n" +
            "\t\tWHERE\n" +
            "\t\t\tlog.RequisitionId = fpbr.id\n" +
            "\t\tORDER BY\n" +
            "\t\t\tCreatedDate ASC)" +
            "\t\tELSE ''\n" +
            "\tEND AS PreviousStatus,\n" +
            "\tfpbr.ActualizedAmount,\n" +
            "\t\n" +
            "\t(\n" +
            "\tSELECT\n" +
            "\t\tTOP 1 fl.CreatedDate\n" +
            "\tFROM\n" +
            "\t\tFPBTransactionLog fl\n" +
            "\tWHERE\n" +
            "\t\tfl.RequisitionId = fpbr.Id\n" +
            "\t\tAND fl.TransactionStatus = 'DISBURSED_LB') AS FundDisbursedDate,\n" +
            "\tCASE\n" +
            "\t\tWHEN EXISTS(\n" +
            "\t\tSELECT\n" +
            "\t\t\tTOP 1 *\n" +
            "\t\tFROM\n" +
            "\t\t\tDocumentReport dr\n" +
            "\t\tWHERE\n" +
            "\t\t\tdr.TransactionId = fpbr.Id\n" +
            "\t\t\tAND dr.DocumentType = 'FPB_BA') THEN CAST (1 AS BIT)\n" +
            "\t\tELSE CAST(0 AS BIT)\n" +
            "\tEND BaExist\n" +
            "FROM\n" +
            "\tFPBRequest fpbr\n" +
            "WHERE\n" +
            "\tfpbr.CreatedBy = :nik\n" +
            "\tAND (DATEADD(HOUR, 7, fpbr.CreatedDate) > DATEADD(MONTH, -2, DATEADD(HOUR, 7 , GETDATE()))\n" +
            "\t\tOR (DATEADD(HOUR, 7, fpbr.CreatedDate) < DATEADD(MONTH, -2, DATEADD(HOUR, 7 , GETDATE()))\n" +
            "\t\t\tAND fpbr.RequestStatus NOT IN ('CANCELLED', 'COMPLETED')))\n" +
            "ORDER BY\n" +
            "\tfpbr.CreatedDate DESC\n")
    List<RequisitionListProjection> findByNik(@Param("nik") String nik);

    @Query(nativeQuery = true, value = "SELECT\n" +
            "\tfpbr.Id,\n" +
            "\tfpbr.RequisitionNo AS RefNo,\n" +
            "\tDATEADD(HOUR,7,fpbr.CreatedDate) AS CreatedDate,\n" +
            "\tcase when fpbr.RequestStatus in ('COMPLETED','COLLECTED_LB','REIMBURSEMENT') " +
            "then (select top 1 DATEADD(HOUR,7,log.CreatedDate) from FPBTransactionLog log where log.RequisitionId = fpbr.id " +
            "AND log.TransactionStatus = 'SETTLEMENT_APPROVED_PIC_KAS' order by CreatedDate desc) " +
            "else null end as SettlementDate, " +
            "\tfpbr.CreatedBy AS CreatedByNik,\n" +
            "\t(\n" +
            "\tSELECT\n" +
            "\t\tTOP 1 cup.COName\n" +
            "\tFROM\n" +
            "\t\tCashUserPersonas cup\n" +
            "\tWHERE\n" +
            "\t\tcup.NIK = fpbr.CreatedBy) AS CreatedByName, \n" +
            "\tfpbr.RequestAmount,\n" +
            "\tfpbr.RequestStatus,\n" +
            "\tcase when fpbr.RequestStatus in ('REQUEST_REJECTED_BM','REQUEST_REJECTED_PIC_KAS','SETTLEMENT_REJECTED_BM','SETTLEMENT_REJECTED_PIC_KAS','RV_REQUESTED','CANCEL_REJECTED_BM') " +
            "then (select top 1 log.Note from FPBTransactionLog log where log.RequisitionId = fpbr.id order by CreatedDate desc) " +
            "else '' end as Note, " +
            "\tCASE\n" +
            "\t\tWHEN fpbr.RequestStatus = 'CANCEL_REJECTED_BM' THEN (\n" +
            "\t\tSELECT\n" +
            "\t\t\tTOP 1 log.TransactionStatus\n" +
            "\t\tFROM\n" +
            "\t\t\t(\n" +
            "\t\t\tSELECT\n" +
            "\t\t\t\tTOP 3 *\n" +
            "\t\t\tFROM\n" +
            "\t\t\t\tFPBTransactionLog fl\n" +
            "\t\t\tWHERE\n" +
            "\t\t\t\tfl.RequisitionId = fpbr.Id\n" +
            "\t\t\tORDER BY\n" +
            "\t\t\t\tfl.CreatedDate DESC) log\n" +
            "\t\tWHERE\n" +
            "\t\t\tlog.RequisitionId = fpbr.id\n" +
            "\t\tORDER BY\n" +
            "\t\t\tCreatedDate ASC)\n" +
            "\t\tWHEN fpbr.RequestStatus = 'CANCEL_REQUESTED' THEN (\n" +
            "\t\tSELECT\n" +
            "\t\t\tTOP 1 log.TransactionStatus\n" +
            "\t\tFROM\n" +
            "\t\t\t(\n" +
            "\t\t\tSELECT\n" +
            "\t\t\t\tTOP 2 *\n" +
            "\t\t\tFROM\n" +
            "\t\t\t\tFPBTransactionLog fl\n" +
            "\t\t\tWHERE\n" +
            "\t\t\t\tfl.RequisitionId = fpbr.Id\n" +
            "\t\t\tORDER BY\n" +
            "\t\t\t\tfl.CreatedDate DESC) log\n" +
            "\t\tWHERE\n" +
            "\t\t\tlog.RequisitionId = fpbr.id\n" +
            "\t\tORDER BY\n" +
            "\t\t\tCreatedDate ASC)" +
            "\t\tWHEN fpbr.RequestStatus = 'RV_REQUESTED' THEN (\n" +
            "\t\tSELECT\n" +
            "\t\t\tTOP 1 log.TransactionStatus\n" +
            "\t\tFROM\n" +
            "\t\t\t(\n" +
            "\t\t\tSELECT\n" +
            "\t\t\t\tTOP 2 *\n" +
            "\t\t\tFROM\n" +
            "\t\t\t\tFPBTransactionLog fl\n" +
            "\t\t\tWHERE\n" +
            "\t\t\t\tfl.RequisitionId = fpbr.Id\n" +
            "\t\t\tORDER BY\n" +
            "\t\t\t\tfl.CreatedDate DESC) log\n" +
            "\t\tWHERE\n" +
            "\t\t\tlog.RequisitionId = fpbr.id\n" +
            "\t\tORDER BY\n" +
            "\t\t\tCreatedDate ASC)" +
            "\t\tELSE ''\n" +
            "\tEND AS PreviousStatus,\n" +
            "\tfpbr.ActualizedAmount\n" +
            "FROM\n" +
            "\tFPBRequest fpbr\n" +
            "WHERE\n" +
            "\tfpbr.MMSCode = :mmsCode\n" +
            "\tAND DATEADD(HOUR, 7, fpbr.CreatedDate) < DATEADD(MONTH, -2, DATEADD(HOUR, 7 , GETDATE()))\n" +
            "\tAND fpbr.RequestStatus IN ('COMPLETED', 'CANCELLED')\n" +
            "ORDER BY\n" +
            "\tfpbr.CreatedDate DESC")
    List<RequisitionListProjection> findHistoryByMmsCodeForList(@Param("mmsCode") String mmsCode);

    @Query(nativeQuery = true, value = "SELECT\n" +
            "\tfpbr.Id,\n" +
            "\tcase when fpbr.RequestStatus in ('COMPLETED','COLLECTED_LB','REIMBURSEMENT') " +
            "then (select top 1 DATEADD(HOUR,7,log.CreatedDate) from FPBTransactionLog log where log.RequisitionId = fpbr.id " +
            "AND log.TransactionStatus = 'SETTLEMENT_APPROVED_PIC_KAS' order by CreatedDate desc) " +
            "else null end as SettlementDate, " +
            "\tfpbr.RequisitionNo AS RefNo,\n" +
            "\tDATEADD(HOUR,\n" +
            "        7,\n" +
            "        fpbr.CreatedDate) AS CreatedDate,\n" +
            "\tfpbr.CreatedBy AS CreatedByNik,\n" +
            "\t(\n" +
            "\tSELECT\n" +
            "\t\tTOP 1 cup.COName\n" +
            "\tFROM\n" +
            "\t\tCashUserPersonas cup\n" +
            "\tWHERE\n" +
            "\t\tcup.NIK = fpbr.CreatedBy) AS CreatedByName,\n" +
            "\tfpbr.RequestAmount,\n" +
            "\tfpbr.RequestStatus,\n" +
            "\tCASE\n" +
            "\t\tWHEN fpbr.RequestStatus IN ('REQUEST_REJECTED_BM',\n" +
            "            'REQUEST_REJECTED_PIC_KAS',\n" +
            "            'SETTLEMENT_REJECTED_BM',\n" +
            "            'SETTLEMENT_REJECTED_PIC_KAS',\n" +
            "            'RV_REQUESTED',\n" +
            "            'CANCEL_REJECTED_BM') THEN (\n" +
            "\t\tSELECT\n" +
            "\t\t\tTOP 1 log.Note\n" +
            "\t\tFROM\n" +
            "\t\t\tFPBTransactionLog log\n" +
            "\t\tWHERE\n" +
            "\t\t\tlog.RequisitionId = fpbr.id\n" +
            "\t\tORDER BY\n" +
            "\t\t\tCreatedDate DESC)\n" +
            "\t\tELSE ''\n" +
            "\tEND AS Note,\n" +
            "\tCASE\n" +
            "\t\tWHEN fpbr.RequestStatus = 'CANCEL_REJECTED_BM' THEN (\n" +
            "\t\tSELECT\n" +
            "\t\t\tTOP 1 log.TransactionStatus\n" +
            "\t\tFROM\n" +
            "\t\t\t(\n" +
            "\t\t\tSELECT\n" +
            "\t\t\t\tTOP 3 *\n" +
            "\t\t\tFROM\n" +
            "\t\t\t\tFPBTransactionLog fl\n" +
            "\t\t\tWHERE\n" +
            "\t\t\t\tfl.RequisitionId = fpbr.Id\n" +
            "\t\t\tORDER BY\n" +
            "\t\t\t\tfl.CreatedDate DESC) log\n" +
            "\t\tWHERE\n" +
            "\t\t\tlog.RequisitionId = fpbr.id\n" +
            "\t\tORDER BY\n" +
            "\t\t\tCreatedDate ASC)\n" +
            "\t\tWHEN fpbr.RequestStatus = 'CANCEL_REQUESTED' THEN (\n" +
            "\t\tSELECT\n" +
            "\t\t\tTOP 1 log.TransactionStatus\n" +
            "\t\tFROM\n" +
            "\t\t\t(\n" +
            "\t\t\tSELECT\n" +
            "\t\t\t\tTOP 2 *\n" +
            "\t\t\tFROM\n" +
            "\t\t\t\tFPBTransactionLog fl\n" +
            "\t\t\tWHERE\n" +
            "\t\t\t\tfl.RequisitionId = fpbr.Id\n" +
            "\t\t\tORDER BY\n" +
            "\t\t\t\tfl.CreatedDate DESC) log\n" +
            "\t\tWHERE\n" +
            "\t\t\tlog.RequisitionId = fpbr.id\n" +
            "\t\tORDER BY\n" +
            "\t\t\tCreatedDate ASC)" +
            "\t\tWHEN fpbr.RequestStatus = 'RV_REQUESTED' THEN (\n" +
            "\t\tSELECT\n" +
            "\t\t\tTOP 1 log.TransactionStatus\n" +
            "\t\tFROM\n" +
            "\t\t\t(\n" +
            "\t\t\tSELECT\n" +
            "\t\t\t\tTOP 2 *\n" +
            "\t\t\tFROM\n" +
            "\t\t\t\tFPBTransactionLog fl\n" +
            "\t\t\tWHERE\n" +
            "\t\t\t\tfl.RequisitionId = fpbr.Id\n" +
            "\t\t\tORDER BY\n" +
            "\t\t\t\tfl.CreatedDate DESC) log\n" +
            "\t\tWHERE\n" +
            "\t\t\tlog.RequisitionId = fpbr.id\n" +
            "\t\tORDER BY\n" +
            "\t\t\tCreatedDate ASC)" +
            "\t\tELSE ''\n" +
            "\tEND AS PreviousStatus,\n" +
            "\tfpbr.ActualizedAmount\n" +
            "FROM\n" +
            "\tFPBRequest fpbr\n" +
            "WHERE\n" +
            "\tfpbr.CreatedBy = :nik\n" +
            "\tAND DATEADD(HOUR, 7, fpbr.CreatedDate) < DATEADD(MONTH, -2, DATEADD(HOUR, 7 , GETDATE()))\n" +
            "\tAND fpbr.RequestStatus IN ('COMPLETED', 'CANCELLED')\n" +
            "ORDER BY\n" +
            "\tfpbr.CreatedDate DESC\n")
    List<RequisitionListProjection> findHistoryByNik(@Param("nik") String nik);

    @Transactional
    @Modifying
    @Query(nativeQuery = true, value = "UPDATE dbo.FPBRequest SET RequisitionNo = :requisitionNo WHERE Id = :id")
    void updateRequisitionNumber(@Param("requisitionNo") String requisitionNo, Long id);

    @Query(nativeQuery = true, value = "SELECT TOP 1\n" +
            "\t*\n" +
            "FROM\n" +
            "\tFPBRequest f\n" +
            "WHERE\n" +
            "\tf.MMSCode = :mmsCode\n" +
            "\tAND f.RequestStatus IN(\n" +
            "'SUBMITTED', \n" +
            "'CANCEL_REQUESTED', \n" +
            "'REQUEST_APPROVED_PIC_KAS', \n" +
            "'REQUEST_APPROVED_BM', \n" +
            "'DISBURSED_LB', \n" +
            "'IN_PROGRESS', \n" +
            "'ACTUALIZED', \n" +
            "'SETTLEMENT_APPROVED_PIC_KAS',\n" +
            "'SETTLEMENT_REJECTED_PIC_KAS',\n" +
            "'SETTLEMENT_APPROVED_BM',\n" +
            "'SETTLEMENT_REJECTED_BM',\n" +
            "'SETTLEMENT_RV_APPROVED_BM',\n" +
            "'COLLECTED_LB',\n" +
            "'REIMBURSEMENT',\n" +
            "'RV_REIMBURSEMENT',\n" +
            "'RV_COLLECTED_LB',\n" +
            "'RV_REQUESTED',\n" +
            "'RV_APPROVED_BM') ORDER BY f.CreatedDate DESC")
    Optional<RequisitionEntity> existsByMmsCodeAndRequestStatusIn(String mmsCode);

    @Query(nativeQuery = true, value = "SELECT\n" +
            "\tTOP 1 log.TransactionStatus\n" +
            "FROM\n" +
            "\t(\n" +
            "\tSELECT\n" +
            "\t\tTOP 2 *\n" +
            "\tFROM\n" +
            "\t\tFPBTransactionLog fl\n" +
            "\tWHERE\n" +
            "\t\tfl.RequisitionId = :requisitionId\n" +
            "\tORDER BY\n" +
            "\t\tfl.CreatedDate DESC) log\n" +
            "ORDER BY\n" +
            "\tCreatedDate ASC")
    RequisitionStatus findPreviousStatus(@Param("requisitionId") Long id);


    @Query(nativeQuery = true, value = "SELECT\n" +
            "\tCASE\n" +
            "\t\tWHEN\n" +
            "EXISTS(\n" +
            "\t\tSELECT\n" +
            "\t\t\t*\n" +
            "\t\tFROM\n" +
            "\t\t\tDocumentReport dr\n" +
            "\t\tWHERE\n" +
            "\t\t\tdr.TransactionId = :requisitionId\n" +
            "\t\t\tAND dr.DocumentType = 'FPB_BA') \n" +
            "\t\tTHEN CAST(1 AS BIT)\n" +
            "\t\tELSE CAST(0 AS BIT)\n" +
            "\tEND")
    boolean getBeritaAcaraStatus(@Param("requisitionId") Long requisitionId);

    @Query(nativeQuery = true, value = "SELECT\n" +
            "\tTOP 1 lt.CreatedBy,\n" +
            "\tlt.TransactionType,\n" +
            "\tlt.TotalAmount,\n" +
            "\tlt.Hundreds,\n" +
            "\tlt.Fiftys,\n" +
            "\tlt.Twentys,\n" +
            "\tlt.Tens,\n" +
            "\tlt.Fives,\n" +
            "\tlt.SmallMoney,\n" +
            "\tlt.TotalAMountRequest\n" +
            "FROM\n" +
            "\tFPBReversal fr\n" +
            "INNER JOIN LBDailyTransaction lt ON\n" +
            "\tlt.id = fr.AdditionalSettlementId\n" +
            "INNER JOIN FPBRequest f ON\n" +
            "\tf.Id = fr.RequisitionId\n" +
            "WHERE\n" +
            "\tfr.RequisitionId = :requisitionId\n" +
            "ORDER BY\n" +
            "\tfr.Id DESC")
    Optional<LBDailyTransactionProjection> fetchReversedReturnTransactionByRequisitionId(@Param("requisitionId") Long id);

    @Query(nativeQuery = true, value = "SELECT\n" +
            "\tlt.CreatedBy,\n" +
            "\tlt.TransactionType,\n" +
            "\tlt.TotalAmount,\n" +
            "\tlt.Hundreds,\n" +
            "\tlt.Fiftys,\n" +
            "\tlt.Twentys,\n" +
            "\tlt.Tens,\n" +
            "\tlt.Fives,\n" +
            "\tlt.SmallMoney,\n" +
            "\tlt.TotalAMountRequest\n" +
            "FROM\n" +
            "\tFPBRequest f\n" +
            "INNER JOIN LBDailyTransaction lt ON\n" +
            "\tlt.id = f.AdditionalSettlementId\n" +
            "WHERE\n" +
            "\tf.Id = :requisitionId")
    Optional<LBDailyTransactionProjection> fetchReturnTransactionByRequisitionId(@Param("requisitionId") Long id);
}
