package com.btpnsyariah.agendaku.fpb.transaction.requisition;

public enum RequisitionStatus {
    SUBMITTED("Menunggu Persetujuan - BM"),
    CANCEL_REQUESTED("Menunggu Persetujuan Pembatalan - BM"),
    CANCEL_APPROVED_BM("Pembatalan Telah Disetujui - BM"),
    CANCEL_REJECTED_BM("Pembatalan Ditolak - BM"),
    CANCELLED("Dibatalkan"),
    REQUEST_APPROVED_BM("Telah Disetujui BM"),
    REQUEST_APPROVED_PIC_KAS("Telah disetujui PIC Kas"),
    DISBURSED_LB("Menunggu Proses Penyelesaian"),
    IN_PROGRESS("Penyelesaian Dalam Proses"),
    ACTUALIZED("Menunggu Persetujuan Penyelesaian - BM"),
    ACTUALIZE_ITEM_AOY("Aktualisasi Barang > 1 Tahun"),
    ACTUALIZE_ITEM_BOY("Aktualisasi Barang < 1 Tahun"),
    REQUEST_REJECTED_BM("Ditolak oleh BM"),
    REQUEST_REJECTED_PIC_KAS("Ditolak oleh PIC Kas"),
    SETTLEMENT_APPROVED_BM("Penyelesaian Telah Disetujui - BM"),
    SETTLEMENT_APPROVED_PIC_KAS("Penyelesaian Telah Disetujui - PIC Kas"),
    SETTLEMENT_REJECTED_BM("Penyelesaian Ditolak - BM"),
    SETTLEMENT_REJECTED_PIC_KAS("Penyelesaian Ditolak - PIC Kas"),
    RV_REQUESTED("Menunggu Persetujuan Koreksi - BM"),
    RV_APPROVED_BM("Koreksi Telah Disetujui - BM"),
    RV_ACTUALIZE_ITEM_AOY("Koreksi Aktualisasi Barang > 1 Tahun"),
    RV_ACTUALIZE_ITEM_BOY("Koreksi Aktualisasi Barang < 1 Tahun"),
    SETTLEMENT_RV_APPROVED_BM("Koreksi Penyelesaian Telah Disetujui - BM"),
    SETTLEMENT_RV_REJECTED_BM("Koreksi Penyelesaian Ditolak - BM"),
    RV_REJECTED_BM("Koreksi Ditolak - BM"),
    REIMBURSEMENT("Nominal kurang - Proses kekurangan nominal di lemari besi"),
    COLLECTED_LB("Nominal lebih - Proses kelebihan nominal di lemari besi"),
    RV_REIMBURSEMENT("Nominal lebih - Proses kelebihan nominal di lemari besi"),
    RV_COLLECTED_LB("Nominal kurang - Proses kekurangan nominal di lemari besi"),
    COMPLETED("Selesai");

    private final String status;

    public String getStatus() {
        return status;
    }

    RequisitionStatus(String requisitionStatus) {
        this.status = requisitionStatus;
    }
}
