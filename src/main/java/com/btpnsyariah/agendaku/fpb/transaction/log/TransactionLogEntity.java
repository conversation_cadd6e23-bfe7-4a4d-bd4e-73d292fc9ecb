package com.btpnsyariah.agendaku.fpb.transaction.log;

import com.btpnsyariah.agendaku.fpb.transaction.requisition.RequisitionStatus;
import lombok.*;

import javax.persistence.*;
import java.math.BigDecimal;
import java.sql.Timestamp;

@Getter
@Setter
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "FPBTransactionLog", schema = "dbo")
public class TransactionLogEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "Id", nullable = false, updatable = false)
    private Long id;

    @Column(name = "RequisitionId")
    private Long requisitionId;

    @Column(name = "CreatedBy")
    private String createdBy;

    @Column(name = "CreatedDate")
    private Timestamp createdDate;

    @Enumerated(EnumType.STRING)
    @Column(name = "TransactionStatus")
    private RequisitionStatus transactionStatus;

    @Column(name = "Note")
    private String note;

    @Column(name = "CreatedByName")
    private String createdByName;

    @Column(name = "XlinkLogId")
    private Long xlinkLogId;

    @Column(name = "Amount")
    private BigDecimal amount;

    @PrePersist
    public void prePersistNulls() {
        if (amount == null) {
            amount = BigDecimal.ZERO;
        }
    }

    public TransactionLogEntity(Long requisitionId, String createdBy, Timestamp createdDate, RequisitionStatus transactionStatus) {
        this.requisitionId = requisitionId;
        this.createdBy = createdBy;
        this.createdDate = createdDate;
        this.transactionStatus = transactionStatus;
    }
}
