package com.btpnsyariah.agendaku.fpb.transaction.requisition;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class RequisitionStatusAndListDTO {
    private List<RequisitionListDTO> list;
    private boolean isRequisitionNotCompleted;
    private String requisitionIsNotCompletedMessage;
}
