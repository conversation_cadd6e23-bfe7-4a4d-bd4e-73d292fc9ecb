package com.btpnsyariah.agendaku.fpb.transaction.invoice;

import com.btpnsyariah.agendaku.fpb.exception.BusinessException;
import com.btpnsyariah.agendaku.fpb.model.BaseResponse;
import com.btpnsyariah.agendaku.fpb.model.ResponseMessage;
import com.btpnsyariah.agendaku.fpb.util.Utility;
import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.multipart.MultipartFile;

@CrossOrigin(origins = {
        "https://core-agendaku-south.apps.south.syariahbtpn.com",
        "https://core-agendaku-north.apps.north.syariahbtpn.com",
        "http://localhost:3000",
        "http://localhost:3001",
        "https://agendaku.apps.btpnsyariah.com",
        "https://core-agendaku-dirty.apps.nww.syariahbtpn.com",
        "https://core-agendaku-alpha.apps.nww.syariahbtpn.com",
        "https://core-agendaku-beta.apps.nww.syariahbtpn.com",
        "https://core-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com"})
@RestController
@RequestMapping("/invoices")
@Api(tags = "Invoices")
public class InvoiceController {

    @Autowired
    private InvoiceService invoiceService;

    private static final Logger LOGGER = LoggerFactory.getLogger(InvoiceController.class);

    private static final String LOG_ERROR = "[InvoiceController] [{}] There was an error in {} : {} | {}";

    @Operation(summary = "Upload invoice", parameters = {
            @Parameter(in = ParameterIn.PATH
                    , description = "RequisitionId"
                    , name = "requisitionId"
                    , content = @Content(schema = @Schema(type = "Long", defaultValue = "null"))),
            @Parameter(in = ParameterIn.PATH
                    , description = "Item ID"
                    , name = "itemId"
                    , content = @Content(schema = @Schema(type = "Long", defaultValue = "null"))),
            @Parameter(in = ParameterIn.HEADER
                    , description = "User Access Token"
                    , name = "Access-Token"
                    , content = @Content(schema = @Schema(type = "String", defaultValue = "null")))
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Invoice uploaded",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) }),
            @ApiResponse(responseCode = "4xx", description = "Failed to upload invoice",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) }),
            @ApiResponse(responseCode = "500", description = "Failed to upload invoice",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) })
    })
    @PostMapping(value = "/upload/{requisitionId}/{itemId}", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    @CrossOrigin(origins = {
            "https://core-agendaku-south.apps.south.syariahbtpn.com",
            "https://core-agendaku-north.apps.north.syariahbtpn.com",
            "http://localhost:3000",
            "http://localhost:3001",
            "https://agendaku.apps.btpnsyariah.com",
            "https://core-agendaku-dirty.apps.nww.syariahbtpn.com",
            "https://core-agendaku-alpha.apps.nww.syariahbtpn.com",
            "https://core-agendaku-beta.apps.nww.syariahbtpn.com", 
            "https://core-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com"})
    public ResponseEntity<BaseResponse> uploadInvoice(
            @PathVariable("requisitionId") Long requisitionId,
            @PathVariable("itemId") Long itemId,
            @RequestPart MultipartFile invoice,
            @RequestHeader("Access-Token") String token) {
        try {
            return invoiceService.uploadInvoice(
                    requisitionId,
                    itemId,
                    token,
                    invoice);
        } catch (BusinessException e) {
            String traceCode = Utility.getTraceCode();
            LOGGER.error(LOG_ERROR,
                    traceCode,
                    Utility.getStackTraceMethodClean(e),
                    e.getMessage(),
                    Utility.getStackTrace(e));
            return new ResponseEntity<>(new BaseResponse(
                    false,
                    String.format(ResponseMessage.SOMETHING_WENT_WRONG_ARG, e.getMessage()),
                    traceCode
            ), e.getHttpStatus());
        } catch(HttpClientErrorException e) {
            String traceCode = Utility.getTraceCode();
            LOGGER.error(LOG_ERROR,
                    traceCode,
                    Utility.getStackTraceMethodClean(e),
                    e.getMessage(),
                    Utility.getStackTrace(e));
            return new ResponseEntity<>(new BaseResponse(
                    false,
                    String.format(ResponseMessage.SOMETHING_WENT_WRONG_ARG, e.getMessage()),
                    traceCode
            ), e.getStatusCode());
        } catch (Exception e) {
            String traceCode = Utility.getTraceCode();
            LOGGER.error(LOG_ERROR,
                    traceCode,
                    Utility.getStackTraceMethodClean(e),
                    e.getMessage(),
                    Utility.getStackTrace(e));
            return new ResponseEntity<>(new BaseResponse(
                    false,
                    ResponseMessage.SOMETHING_WENT_WRONG,
                    traceCode
            ), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }


    @Operation(summary = "Get invoice", parameters = {
            @Parameter(in = ParameterIn.PATH
                    , description = "Invoice ID"
                    , name = "invoiceId"
                    , content = @Content(schema = @Schema(type = "String", defaultValue = "null"))),
            @Parameter(in = ParameterIn.HEADER
                    , description = "User Access Token"
                    , name = "Access-Token"
                    , content = @Content(schema = @Schema(type = "String", defaultValue = "null")))
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Invoice fetched",
                    content = { @Content(mediaType = "application/octet-stream") }),
            @ApiResponse(responseCode = "4xx", description = "Failed to upload invoice",
                    content = { @Content(mediaType = "application/octet-stream") }),
            @ApiResponse(responseCode = "500", description = "Failed to upload invoice",
                    content = { @Content(mediaType = "application/octet-stream") })
    })
    @GetMapping("/{invoiceId}")
    @CrossOrigin(origins = {
            "https://core-agendaku-south.apps.south.syariahbtpn.com",
            "https://core-agendaku-north.apps.north.syariahbtpn.com",
            "http://localhost:3000",
            "http://localhost:3001",
            "https://agendaku.apps.btpnsyariah.com",
            "https://core-agendaku-dirty.apps.nww.syariahbtpn.com",
            "https://core-agendaku-alpha.apps.nww.syariahbtpn.com",
            "https://core-agendaku-beta.apps.nww.syariahbtpn.com", 
            "https://core-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com"})
    public ResponseEntity getInvoice(
            @PathVariable("invoiceId") String invoiceId,
            @RequestHeader("Access-Token") String token) {
        try {
            return invoiceService.getInvoice(token, invoiceId);
        } catch(HttpClientErrorException e) {
            String traceCode = Utility.getTraceCode();
            LOGGER.error(LOG_ERROR,
                    traceCode,
                    Utility.getStackTraceMethodClean(e),
                    e.getMessage(),
                    Utility.getStackTrace(e));
            return new ResponseEntity<>(new BaseResponse(
                    false,
                    String.format(ResponseMessage.SOMETHING_WENT_WRONG_ARG, e.getMessage()),
                    traceCode
            ), e.getStatusCode());
        } catch (Exception e) {
            String traceCode = Utility.getTraceCode();
            LOGGER.error(LOG_ERROR,
                    traceCode,
                    Utility.getStackTraceMethodClean(e),
                    e.getMessage(),
                    Utility.getStackTrace(e));
            return new ResponseEntity<>(new BaseResponse(
                    false,
                    ResponseMessage.SOMETHING_WENT_WRONG,
                    traceCode
            ), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Delete invoice", parameters = {
            @Parameter(in = ParameterIn.PATH
                    , description = "Invoice ID"
                    , name = "invoiceId"
                    , content = @Content(schema = @Schema(type = "String", defaultValue = "null"))),
            @Parameter(in = ParameterIn.HEADER
                    , description = "User Access Token"
                    , name = "Access-Token"
                    , content = @Content(schema = @Schema(type = "String", defaultValue = "null")))
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Invoice deleted",
                    content = { @Content(mediaType = "application/json") }),
            @ApiResponse(responseCode = "4xx", description = "Failed to delete invoice",
                    content = { @Content(mediaType = "application/json") }),
            @ApiResponse(responseCode = "500", description = "Failed to delete invoice",
                    content = { @Content(mediaType = "application/json") })
    })
    @DeleteMapping("delete/{invoiceId}")
    @CrossOrigin(origins = {
            "https://core-agendaku-south.apps.south.syariahbtpn.com",
            "https://core-agendaku-north.apps.north.syariahbtpn.com",
            "http://localhost:3000",
            "http://localhost:3001",
            "https://agendaku.apps.btpnsyariah.com",
            "https://core-agendaku-dirty.apps.nww.syariahbtpn.com",
            "https://core-agendaku-alpha.apps.nww.syariahbtpn.com",
            "https://core-agendaku-beta.apps.nww.syariahbtpn.com", 
            "https://core-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com"})
    public ResponseEntity deleteInvoice(
            @PathVariable("invoiceId") String invoiceId,
            @RequestHeader("Access-Token") String token) {
        try {
            return invoiceService.deleteInvoice(token, invoiceId);
        } catch (BusinessException e) {
            String traceCode = Utility.getTraceCode();
            LOGGER.error(LOG_ERROR,
                    traceCode,
                    Utility.getStackTraceMethodClean(e),
                    e.getMessage(),
                    Utility.getStackTrace(e));
            return new ResponseEntity<>(new BaseResponse(
                    false,
                    String.format(ResponseMessage.SOMETHING_WENT_WRONG_ARG, e.getMessage()),
                    traceCode
            ), e.getHttpStatus());
        } catch(HttpClientErrorException e) {
            String traceCode = Utility.getTraceCode();
            LOGGER.error(LOG_ERROR,
                    traceCode,
                    Utility.getStackTraceMethodClean(e),
                    e.getMessage(),
                    Utility.getStackTrace(e));
            return new ResponseEntity<>(new BaseResponse(
                    false,
                    String.format(ResponseMessage.SOMETHING_WENT_WRONG_ARG, e.getMessage()),
                    traceCode
            ), e.getStatusCode());
        } catch (Exception e) {
            String traceCode = Utility.getTraceCode();
            LOGGER.error(LOG_ERROR,
                    traceCode,
                    Utility.getStackTraceMethodClean(e),
                    e.getMessage(),
                    Utility.getStackTrace(e));
            return new ResponseEntity<>(new BaseResponse(
                    false,
                    ResponseMessage.SOMETHING_WENT_WRONG,
                    traceCode
            ), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
