package com.btpnsyariah.agendaku.fpb.transaction.requisition;

import lombok.*;

import javax.persistence.*;
import java.math.BigDecimal;
import java.sql.Timestamp;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Builder
@Table(name = "FPBReversal", schema = "dbo")
public class ReversalEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "Id", nullable = false, updatable = false)
    private Long id;

    @Column(name = "RequisitionId")
    private Long requisitionId;

    @Column(name = "CreatedDate")
    private Timestamp createdDate;

    @Column(name = "ApprovalLog")
    private Long approvalLog;

    @Column(name = "SettledAmount")
    private BigDecimal settledAmount;


    @Column(name = "ActualizedAmount")
    private BigDecimal actualizedmount;


    @Column(name = "ReverseSettlementId")
    private Long reverseSettlementId;

    @Column(name = "AdditionalSettlementId")
    private Long additionalSettlementId;

    @Column(name = "RefundId")
    private Long refundId;

    @Column(name = "CreatedBy")
    private String createdBy;

    @PrePersist
    public void prePersistNulls() {
        if(settledAmount == null) {
            settledAmount = BigDecimal.ZERO;
        }
    }
}
