package com.btpnsyariah.agendaku.fpb.transaction.log;

import com.btpnsyariah.agendaku.fpb.exception.BusinessException;
import com.btpnsyariah.agendaku.fpb.transaction.requisition.RequisitionEntity;
import com.btpnsyariah.agendaku.fpb.transaction.requisition.RequisitionStatus;

import java.math.BigDecimal;

public interface TransactionLogService {

    TransactionLogEntity logRequisition(RequisitionEntity requisitionEntity) throws BusinessException;

    TransactionLogEntity logRequisition(Long requisitionId, RequisitionStatus requisitionStatus, String userId, String note) throws BusinessException;

    TransactionLogEntity logRequisition(Long requisitionId, RequisitionStatus requisitionStatus, String userId, String note, BigDecimal amount) throws BusinessException;

    boolean checkRequisitionTransactionLogStatus(Long requisitionId, String requisitionStatus);

}
