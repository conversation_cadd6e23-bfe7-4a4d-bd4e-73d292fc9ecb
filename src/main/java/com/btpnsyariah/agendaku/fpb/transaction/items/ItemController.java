package com.btpnsyariah.agendaku.fpb.transaction.items;

import com.btpnsyariah.agendaku.fpb.exception.BusinessException;
import com.btpnsyariah.agendaku.fpb.model.BaseResponse;
import com.btpnsyariah.agendaku.fpb.model.ResponseMessage;
import com.btpnsyariah.agendaku.fpb.util.Utility;
import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

@CrossOrigin(origins = {
        "https://core-agendaku-south.apps.south.syariahbtpn.com",
        "https://core-agendaku-north.apps.north.syariahbtpn.com",
        "http://localhost:3000",
        "http://localhost:3001",
        "https://agendaku.apps.btpnsyariah.com",
        "https://core-agendaku-dirty.apps.nww.syariahbtpn.com",
        "https://core-agendaku-alpha.apps.nww.syariahbtpn.com",
        "https://core-agendaku-beta.apps.nww.syariahbtpn.com",
        "https://core-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com"})
@RestController
@RequestMapping("/requisitions/items")
@Api(tags = "Requisition items")
public class ItemController {

    @Autowired
    ItemService itemService;

    private static final Logger LOGGER = LoggerFactory.getLogger(ItemController.class);

    private static final String LOG_ERROR = "[ItemController] [{}] There was an error in {} : {} | {}";

    @Operation(summary = "Get requisition list of items")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Requisition item list fetched",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) }),
            @ApiResponse(responseCode = "500", description = "Failed to fetch requisition item list",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) })
    })
    @GetMapping()
    @CrossOrigin(origins = {
            "https://core-agendaku-south.apps.south.syariahbtpn.com",
            "https://core-agendaku-north.apps.north.syariahbtpn.com",
            "http://localhost:3000",
            "http://localhost:3001",
            "https://agendaku.apps.btpnsyariah.com",
            "https://core-agendaku-dirty.apps.nww.syariahbtpn.com",
            "https://core-agendaku-alpha.apps.nww.syariahbtpn.com",
            "https://core-agendaku-beta.apps.nww.syariahbtpn.com",
            "https://core-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com"})
    public ResponseEntity<BaseResponse> getRequisitionItemList(@RequestParam("requisitionId") Long requisitionId) {
        try {
            return ResponseEntity.ok(new BaseResponse(
                    true,
                    "Requisition item list fetched",
                    itemService.getRequisitionItemList(requisitionId)
            ));
        } catch (BusinessException e) {
            String traceCode = Utility.getTraceCode();
            LOGGER.error(LOG_ERROR,
                    traceCode,
                    Utility.getStackTraceMethodClean(e),
                    e.getMessage(),
                    Utility.getStackTrace(e));
            return new ResponseEntity<>(new BaseResponse(
                    false,
                    String.format(ResponseMessage.SOMETHING_WENT_WRONG_ARG, e.getMessage()),
                    traceCode
            ), e.getHttpStatus());
        } catch (Exception e) {
            String traceCode = Utility.getTraceCode();
            LOGGER.error(LOG_ERROR,
                    traceCode,
                    Utility.getStackTraceMethodClean(e),
                    e.getMessage(),
                    Utility.getStackTrace(e));
            return new ResponseEntity<>(new BaseResponse(
                    false,
                    ResponseMessage.SOMETHING_WENT_WRONG,
                    traceCode
            ), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Get requisition list of item prices by mutliple requisition ids")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Requisition item list fetched",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) }),
            @ApiResponse(responseCode = "500", description = "Failed to fetch requisition item list",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) })
    })
    @GetMapping("/ids")
    public ResponseEntity<BaseResponse> getRequisitionItemListByRequisitionIds(@RequestBody List<Long> requisitionIds) {
        try {
            return ResponseEntity.ok(new BaseResponse(
                    true,
                    "Requisition item list fetched",
                    itemService.getItemsByRequisitionIds(requisitionIds)
            ));
        } catch (Exception e) {
            String traceCode = Utility.getTraceCode();
            LOGGER.error(LOG_ERROR,
                    traceCode,
                    Utility.getStackTraceMethodClean(e),
                    e.getMessage(),
                    Utility.getStackTrace(e));
            return new ResponseEntity<>(new BaseResponse(
                    false,
                    ResponseMessage.SOMETHING_WENT_WRONG,
                    traceCode
            ), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Cancel requisitioned items")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Item cancelled",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) }),
            @ApiResponse(responseCode = "4xx", description = "Either Item ID or Requisition ID not found",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) }),
            @ApiResponse(responseCode = "500", description = "Failed to cancel item",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) })
    })
    @PatchMapping("/cancel/{requisitionId}")
    public ResponseEntity<BaseResponse> cancelRequisitionItem(@PathVariable("requisitionId") Long requisitionId,
                                                              @RequestParam(value = "cancelAll") boolean cancelAll,
                                                              @RequestParam(required = false, value = "itemId") Long itemId,
                                                              @RequestHeader("UserId") String userId) {
        try {
            itemService.cancelItem(cancelAll, requisitionId, itemId, userId);
            return ResponseEntity.ok(new BaseResponse(
                    true,
                    "Item cancelled"
            ));
        } catch (BusinessException e) {
            String traceCode = Utility.getTraceCode();
            LOGGER.error(LOG_ERROR,
                    traceCode,
                    Utility.getStackTraceMethodClean(e),
                    e.getMessage(),
                    Utility.getStackTrace(e));
            return new ResponseEntity<>(new BaseResponse(
                    false,
                    String.format(ResponseMessage.SOMETHING_WENT_WRONG_ARG, e.getMessage()),
                    traceCode
            ), e.getHttpStatus());
        } catch (Exception e) {
            String traceCode = Utility.getTraceCode();
            LOGGER.error(LOG_ERROR,
                    traceCode,
                    Utility.getStackTraceMethodClean(e),
                    e.getMessage(),
                    Utility.getStackTrace(e));
            return new ResponseEntity<>(new BaseResponse(
                    false,
                    ResponseMessage.SOMETHING_WENT_WRONG,
                    traceCode
            ), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Payment utilization")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Payment utilized",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) }),
            @ApiResponse(responseCode = "4xx", description = "Either Item ID or Requisition ID not found",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) }),
            @ApiResponse(responseCode = "500", description = "Failed to utilized payment",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) })
    })
    @PatchMapping("/payment/utilize/{requisitionId}/{itemId}")
    public ResponseEntity<BaseResponse> paymentUtilization(@PathVariable("requisitionId") Long requisitionId,
                                                           @PathVariable("itemId") Long itemId,
                                                           @RequestBody BigDecimal actualAmount) {
        try {
            itemService.utilizePayment(requisitionId, itemId, actualAmount);
            return ResponseEntity.ok(new BaseResponse(
                    true,
                    "Payment actualized"
            ));
        } catch (BusinessException e) {
            String traceCode = Utility.getTraceCode();
            LOGGER.error(LOG_ERROR,
                    traceCode,
                    Utility.getStackTraceMethodClean(e),
                    e.getMessage(),
                    Utility.getStackTrace(e));
            return new ResponseEntity<>(new BaseResponse(
                    false,
                    String.format(ResponseMessage.SOMETHING_WENT_WRONG_ARG, e.getMessage()),
                    traceCode
            ), e.getHttpStatus());
        } catch (Exception e) {
            String traceCode = Utility.getTraceCode();
            LOGGER.error(LOG_ERROR,
                    traceCode,
                    Utility.getStackTraceMethodClean(e),
                    e.getMessage(),
                    Utility.getStackTrace(e));
            return new ResponseEntity<>(new BaseResponse(
                    false,
                    ResponseMessage.SOMETHING_WENT_WRONG,
                    traceCode
            ), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Persist price automatically in FPBItemCatalogue table")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Price persisted",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) }),
            @ApiResponse(responseCode = "4xx", description = "Either Item ID or Requisition ID not found",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) }),
            @ApiResponse(responseCode = "500", description = "Failed to persist price",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) })
    })
    @PatchMapping("/update/auto/{status}")
    public ResponseEntity<BaseResponse> updateItemPriceAutomatically(
            @PathVariable(required = false, value = "status") String status,
            @RequestParam(required = false, value = "all") boolean all,
            @RequestBody(required = false) List<Long> requisitionIds) {
        try {
            itemService.updateItemPriceByRequisitionIds(status, all, requisitionIds);
            return ResponseEntity.ok(new BaseResponse(
                    true,
                    "Price persisted"
            ));
        } catch (Exception e) {
            String traceCode = Utility.getTraceCode();
            LOGGER.error(LOG_ERROR,
                    traceCode,
                    Utility.getStackTraceMethodClean(e),
                    e.getMessage(),
                    Utility.getStackTrace(e));
            return new ResponseEntity<>(new BaseResponse(
                    false,
                    ResponseMessage.SOMETHING_WENT_WRONG,
                    traceCode
            ), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Persist price manually in FPBItemCatalogue table")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Price persisted",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) }),
            @ApiResponse(responseCode = "4xx", description = "Either Item ID or Requisition ID not found",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) }),
            @ApiResponse(responseCode = "500", description = "Failed to persist price",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) })
    })
    @PutMapping("/update/manual")
    public ResponseEntity<BaseResponse> updateItemPriceManually(@RequestBody List<ItemPriceListDTO> itemPriceListDTOS) {
        try {
            itemService.manualUpdateItemPriceByRequisitionIds(itemPriceListDTOS);
            return ResponseEntity.ok(new BaseResponse(
                    true,
                    "Price persisted"
            ));
        } catch (Exception e) {
            String traceCode = Utility.getTraceCode();
            LOGGER.error(LOG_ERROR,
                    traceCode,
                    Utility.getStackTraceMethodClean(e),
                    e.getMessage(),
                    Utility.getStackTrace(e));
            return new ResponseEntity<>(new BaseResponse(
                    false,
                    ResponseMessage.SOMETHING_WENT_WRONG,
                    traceCode
            ), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

}
