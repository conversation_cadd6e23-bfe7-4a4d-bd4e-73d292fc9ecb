package com.btpnsyariah.agendaku.fpb.transaction.invoice;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Set;

public interface InvoiceRepository extends JpaRepository<InvoiceEntity, Long> {

    @Transactional
    @Modifying
    @Query(nativeQuery = true, value = "UPDATE FPBInvoice SET IsDeleted = 1 WHERE InvoiceId = :invoiceId")
    void deleteInvoice(@Param("invoiceId") String invoiceId);

    @Query(nativeQuery = true, value = "SELECT InvoiceId FROM FPBInvoice WHERE RequisitionItemId = :itemId AND IsDeleted = 0")
    Set<String> findInvoiceByRequisitionItemId(@Param("itemId") Long itemId);

    @Transactional
    @Modifying
    @Query(nativeQuery = true, value = "UPDATE\n" +
            "\tFPBInvoice\n" +
            "SET\n" +
            "\tIsDeleted = 1\n" +
            "WHERE\n" +
            "\tRequisitionItemId IN (SELECT Id FROM FPBRequestItems WHERE RequisitionId = :requisitionId)")
    void reverseItemInvoices(@Param("requisitionId") Long requisitionId);
}
