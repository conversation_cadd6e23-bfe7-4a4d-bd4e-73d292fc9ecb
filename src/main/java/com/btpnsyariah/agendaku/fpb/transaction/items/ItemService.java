package com.btpnsyariah.agendaku.fpb.transaction.items;

import com.btpnsyariah.agendaku.fpb.exception.BusinessException;
import com.btpnsyariah.agendaku.fpb.transaction.requisition.RequisitionEntity;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

public interface ItemService {

    List<RequisitionItemListDTO> getRequisitionItemList(Long requisitionId) throws BusinessException;

    @Transactional(propagation = Propagation.REQUIRES_NEW, isolation = Isolation.READ_UNCOMMITTED, rollbackFor = {Exception.class})
    void cancelItem(boolean cancelAll, Long requisitionId, Long itemId, String userId) throws BusinessException;

    @Transactional(propagation = Propagation.REQUIRES_NEW, isolation = Isolation.READ_UNCOMMITTED, rollbackFor = {Exception.class})
    void utilizePayment(Long requisitionId, Long itemId, BigDecimal actualAmount) throws BusinessException;

    void itemExist(RequisitionEntity requisitionEntity, Long itemId) throws BusinessException;

    void checkOverlimitItemPrice(RequisitionEntity requisitionEntity, Long itemId, BigDecimal actualAmount) throws BusinessException;

    void uploadPayment(Long requisitionId, Long itemId, String invoiceId);

    BigDecimal calculateActualization(RequisitionEntity requisitionEntity);

    RequisitionItemEntity findItemByInvoiceId(String invoiceId) throws BusinessException;

    void checkAndUpdatePaymentStatusByInvoiceId(String invoiceId) throws BusinessException;

    RequisitionItemEntity findItemByRequisitionIdAndItemId(Long requisitionId, Long itemId) throws BusinessException;

    List<ItemPriceListDTO> getItemsByRequisitionIds(List<Long> requisitionIds);

    void reverseRequisitionItems(Long requisitionId);

    void revertItemStatus(Set<RequisitionItemEntity> requisitionItems);

    RequisitionEntity filterCancelledItems(RequisitionEntity requisitionById, long[] cancelledItemIds);

    void updateItemPriceByRequisitionIds(String status, boolean all, List<Long> requisitionIds);

    void manualUpdateItemPriceByRequisitionIds(List<ItemPriceListDTO> itemEntities);
}
