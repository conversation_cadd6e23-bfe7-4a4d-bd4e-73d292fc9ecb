package com.btpnsyariah.agendaku.fpb.transaction.requisition;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.sql.Timestamp;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class RequisitionListDTO {

    private Long requestId;
    private String refNo;
    private Timestamp createdDate;
    private Timestamp settlementDate;
    private String createdByNik;
    private String createdByName;
    private BigDecimal requestAmount;
    private BigDecimal actualizedAmount;
    private RequisitionStatus requestStatus;
    private String note;
    private RequisitionStatus previousStatus;
    private int overaging;
    private boolean baExist;

    public RequisitionListDTO(Long requestId, String refNo, Timestamp createdDate, Timestamp settlementDate, String createdByNik, String createdByName, BigDecimal requestAmount, BigDecimal actualizedAmount, RequisitionStatus requestStatus, String note) {
        this.requestId = requestId;
        this.refNo = refNo;
        this.createdDate = createdDate;
        this.settlementDate = settlementDate;
        this.createdByNik = createdByNik;
        this.createdByName = createdByName;
        this.requestAmount = requestAmount;
        this.actualizedAmount = actualizedAmount;
        this.requestStatus = requestStatus;
        this.note = note;
    }

    public RequisitionListDTO(Long requestId, String refNo, Timestamp createdDate, Timestamp settlementDate, String createdByNik, String createdByName, BigDecimal requestAmount, BigDecimal actualizedAmount, RequisitionStatus requestStatus, String note, int overaging) {
        this.requestId = requestId;
        this.refNo = refNo;
        this.createdDate = createdDate;
        this.settlementDate = settlementDate;
        this.createdByNik = createdByNik;
        this.createdByName = createdByName;
        this.requestAmount = requestAmount;
        this.actualizedAmount = actualizedAmount;
        this.requestStatus = requestStatus;
        this.note = note;
        this.overaging = overaging;
    }

    public RequisitionListDTO(Long requestId, String refNo, Timestamp createdDate, Timestamp settlementDate, String createdByNik, String createdByName, BigDecimal requestAmount, BigDecimal actualizedAmount, RequisitionStatus requestStatus, String note, RequisitionStatus previousStatus) {
        this.requestId = requestId;
        this.refNo = refNo;
        this.createdDate = createdDate;
        this.settlementDate = settlementDate;
        this.createdByNik = createdByNik;
        this.createdByName = createdByName;
        this.requestAmount = requestAmount;
        this.actualizedAmount = actualizedAmount;
        this.requestStatus = requestStatus;
        this.note = note;
        this.previousStatus = previousStatus;
    }


}
