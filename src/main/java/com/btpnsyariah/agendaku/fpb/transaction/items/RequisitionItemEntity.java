package com.btpnsyariah.agendaku.fpb.transaction.items;


import com.btpnsyariah.agendaku.fpb.catalogue.CatalogueEntity;
import com.btpnsyariah.agendaku.fpb.transaction.invoice.InvoiceEntity;
import com.btpnsyariah.agendaku.fpb.transaction.requisition.RequisitionEntity;
import com.fasterxml.jackson.annotation.JsonBackReference;
import lombok.*;

import javax.persistence.*;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import java.util.Set;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "FPBRequestItems", schema = "dbo")
public class RequisitionItemEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "Id", updatable = false)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonBackReference
    @JoinColumn(name = "RequisitionId")
    private RequisitionEntity requisitionEntity;

    @Column(name = "ItemId")
    private Long itemId;

    @Column(name = "ItemQty")
    private int itemQty;

    @Column(name = "CreatedDate")
    private Timestamp createdDate;

    @Enumerated(EnumType.STRING)
    @Column(name = "PaymentStatus")
    private PaymentStatus paymentStatus;

    @Column(name = "ActualAmount")
    private BigDecimal actualAmount;

    @Column(name = "Price")
    private BigDecimal price;

    @OneToMany(mappedBy = "requisitionItemEntity")
    private Set<InvoiceEntity> requisitionItemInvoices;

    @OneToOne(cascade = CascadeType.ALL)
    @JoinColumn(name = "ItemId", referencedColumnName = "Id", insertable = false,updatable = false)
    private CatalogueEntity itemDetail;

    @PrePersist
    public void prePersistNulls() {
        if (createdDate == null) {
            createdDate = new Timestamp(new Date().getTime());
        }

        if(paymentStatus == null) {
            paymentStatus = PaymentStatus.CREATED;
        }

        if(actualAmount == null) {
            actualAmount = BigDecimal.ZERO;
        }

        if (price == null) {
            price = BigDecimal.ZERO;
        }
    }
}
