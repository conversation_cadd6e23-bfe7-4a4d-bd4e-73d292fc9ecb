package com.btpnsyariah.agendaku.fpb.transaction.log;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface TransactionLogRepository extends JpaRepository<TransactionLogEntity, Long> {

    @Query(nativeQuery = true, value = "SELECT\n" +
            "\tCASE\n" +
            "\t\tWHEN EXISTS(\n" +
            "\t\tSELECT\n" +
            "\t\t\t*\n" +
            "\t\tFROM\n" +
            "\t\t\tFPBTransactionLog fl\n" +
            "\t\tWHERE\n" +
            "\t\t\tfl.RequisitionId = :requisitionId\n" +
            "\t\t\tAND fl.TransactionStatus = :requisitionStatus)\n" +
            "THEN 'true'\n" +
            "\t\tELSE 'false'\n" +
            "\t\tEND")
    boolean checkRequisitionTransactionLogStatus(@Param("requisitionId") Long requisitionId, @Param("requisitionStatus") String requisitionStatus);
}
