package com.btpnsyariah.agendaku.fpb.transaction.xlink;

import com.btpnsyariah.agendaku.fpb.model.RequestedType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.math.BigDecimal;
import java.sql.Timestamp;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "XlinkLog", schema = "dbo")
public class XlinkLogEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "CreatedDate")
    private Timestamp createdDate;

    @Column(name = "CreatedBy")
    private String createdBy;

    @Column(name = "UpdatedDate")
    private Timestamp updatedDate;

    @Column(name = "UpdatedBy")
    private String updatedBy;

    @Column(name = "TransactionId")
    private Long transactionId;

    @Enumerated(EnumType.STRING)
    @Column(name = "RequestedType")
    private RequestedType requestedType;

    @Column(name = "TransactionStatus")
    private String transactionStatus;

    @Column(name = "ResponseCode")
    private Integer responseCode;

    @Column(name = "ResponseMessage")
    private String responseMessage;

    @Column(name = "MMSCode")
    private String mmsCode;

    @Column(name = "DeviceId")
    private String deviceId;

    @Column(name = "IsOnPosting")
    private boolean isOnPosting;

    @Column(name = "IsRetry")
    private Boolean isRetry;

    @Column(name = "Counter")
    private int counter;

    @Column(name = "FTNumber")
    private String ftNumber;

    @Column(name = "Amount")
    private BigDecimal amount;
}
