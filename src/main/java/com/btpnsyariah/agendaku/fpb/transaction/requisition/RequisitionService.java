package com.btpnsyariah.agendaku.fpb.transaction.requisition;

import com.btpnsyariah.agendaku.fpb.exception.BusinessException;
import com.btpnsyariah.agendaku.fpb.model.BaseResponse;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

public interface RequisitionService {
    RequisitionStatusAndListDTO getRequisitions(String nik,String nikParam, boolean approval, boolean history, String mmsCode) throws BusinessException;

    @Transactional(propagation = Propagation.REQUIRES_NEW, isolation = Isolation.READ_UNCOMMITTED, rollbackFor = {Exception.class})
    String submitRequisition(String userId, String mmsCode, RequisitionEntity requisitionEntity) throws BusinessException;

    @Transactional(propagation = Propagation.REQUIRES_NEW, isolation = Isolation.READ_UNCOMMITTED, rollbackFor = {Exception.class})
    void submitApprovalRequisitions(RequisitionApprovalDTO requisitionApprovalDTO, String userId, String accessToken, String coCode, String rrn,String deviceId) throws BusinessException;

    void logAndUpdateRequisitionStatus(
            RequisitionEntity requisitionEntity,
            RequisitionStatus requisitionStatus,
            String userId,
            String note) throws BusinessException;

    void validateRequisition(Long requisitionId) throws BusinessException;

    RequisitionEntity findRequisitionById(Long requisitionId) throws BusinessException;

    @Transactional(propagation = Propagation.REQUIRES_NEW, isolation = Isolation.READ_UNCOMMITTED, rollbackFor = {Exception.class})
    void submitRequisitionSettlement(Long requisitionId, String userId, long[] cancelledItemIds) throws BusinessException;

    void requestCancelRequisition(Long requisitionId, String userId, String note) throws BusinessException;

    void requestReverseRequisition(Long requisitionId, boolean settlement, String userId, String note) throws BusinessException;

    ResponseEntity<BaseResponse> uploadBeritaAcara(Long requisitionId, String token, MultipartFile beritaAcara) throws BusinessException, IOException;

    ResponseEntity<BaseResponse> getBeritaAcara(Long requisitionId, String token) throws BusinessException;

    boolean getBeritaAcaraStatus(Long requisitionId) throws BusinessException;

    void changePic(Long requisitionId, String nikPic, String userId) throws BusinessException;
}
