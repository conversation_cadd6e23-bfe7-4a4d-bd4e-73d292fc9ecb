package com.btpnsyariah.agendaku.fpb.transaction.requisition;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.sql.Timestamp;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RequisitionDTO {

    private Long requestId;

    private String mmsCode;

    private String createdBy;

    private Timestamp createdDate;

    private BigDecimal requestAmount;

    private RequisitionStatus requestStatus;

}
