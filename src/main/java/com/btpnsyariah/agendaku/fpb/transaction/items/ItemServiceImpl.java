package com.btpnsyariah.agendaku.fpb.transaction.items;

import com.btpnsyariah.agendaku.fpb.catalogue.CatalogueEntity;
import com.btpnsyariah.agendaku.fpb.catalogue.CatalogueService;
import com.btpnsyariah.agendaku.fpb.exception.BusinessException;
import com.btpnsyariah.agendaku.fpb.model.ResponseMessage;
import com.btpnsyariah.agendaku.fpb.transaction.invoice.InvoiceService;
import com.btpnsyariah.agendaku.fpb.transaction.requisition.RequisitionEntity;
import com.btpnsyariah.agendaku.fpb.transaction.requisition.RequisitionService;
import com.btpnsyariah.agendaku.fpb.transaction.requisition.RequisitionStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.btpnsyariah.agendaku.fpb.model.Predicates.*;
import static com.btpnsyariah.agendaku.fpb.model.Constant.*;


@Component
@Slf4j
public class ItemServiceImpl implements ItemService{
    public static final String CANCEL_REQUISITION_BY_CANCELLING_ALL_ITEMS = "Batal pengajuan karena semua barang dibatalkan";
    public static final String PAYMENT_UTILIZATION = "Utilisasi pembayaran";
    public static final BigDecimal ROUNDED_AMOUNT = new BigDecimal("500.00");

    public static final BigDecimal ItemMaxPriceLimitMultiply = new BigDecimal("1.2");
    protected static final List<RequisitionStatus> actualizationStatuses = new ArrayList<>(List.of(
            RequisitionStatus.DISBURSED_LB,
            RequisitionStatus.IN_PROGRESS,
            RequisitionStatus.RV_APPROVED_BM,
            RequisitionStatus.SETTLEMENT_REJECTED_BM,
            RequisitionStatus.SETTLEMENT_REJECTED_PIC_KAS
    ));

    @Autowired
    RequisitionService requisitionService;

    @Autowired
    ItemRepository itemRepository;

    @Autowired
    CatalogueService catalogueService;

    @Lazy
    @Autowired
    InvoiceService invoiceService;

    @Override
    public List<RequisitionItemListDTO> getRequisitionItemList(Long requisitionId) throws BusinessException {

        requisitionService.validateRequisition(requisitionId);

        return itemRepository.findItemListByRequisitionId(requisitionId)
                .stream()
                .map(r -> new RequisitionItemListDTO(
                        r.getItemId(),
                        r.getItemName(),
                        r.getItemCategory(),
                        r.getItemQty(),
                        r.getMeasurementUnit(),
                        r.getItemLifeSpan().getItemLifespanValue(),
                        r.getPrice(),
                        r.getActualAmount(),
                        r.getPaymentStatus(),
                        invoiceService.findInvoiceByRequisitionItemId(r.getId())
                )).collect(Collectors.toList());
    }

    @Override
    public void cancelItem(boolean cancelAll, Long requisitionId, Long itemId, String userId) throws BusinessException {
        RequisitionEntity requisitionEntity = requisitionService.findRequisitionById(requisitionId);

        if(actualizationStatuses.contains(requisitionEntity.getRequestStatus())) {
            if(cancelAll) {
                itemRepository.cancelItemsByRequisitionId(requisitionId);
                requisitionService.requestCancelRequisition(requisitionId, userId, REQUISITION_CANCELLATION);
            } else {
                itemExist(requisitionEntity, itemId);
                if(moreThanOneItem(requisitionEntity)) {
                    itemRepository.cancelItemByRequisitionIdAndItemId(requisitionId, itemId);
                } else {
                    itemRepository.cancelItemByRequisitionIdAndItemId(requisitionId, itemId);
                    requisitionService.requestCancelRequisition(requisitionId, userId, REQUISITION_CANCELLATION);
                }
            }
        } else {
            throw new BusinessException(HttpStatus.BAD_REQUEST, ResponseMessage.STATUS_NOT_MATCH);
        }
    }

    // TODO : To be removed - not needed anymore since users are now able to cancel actualized items
    private void validateItemPaymentStatus(Set<RequisitionItemEntity> requisitionItems, Long itemId) throws BusinessException {
        if(requisitionItems
                .stream()
                .filter(i -> i.getItemId().equals(itemId))
                .allMatch(PAYMENT_STATUS_IS_NOT_CREATED)) {
            throw new BusinessException(HttpStatus.BAD_REQUEST, ResponseMessage.INVALID_CANCEL_ITEM);
        }
    }

    // TODO : To be removed - not needed anymore since users are now able to cancel actualized items
    private void validateAllItemPaymentStatus(Set<RequisitionItemEntity> requisitionItems) throws BusinessException {
        if(requisitionItems
                .stream()
                .filter(PAYMENT_STATUS_IS_NOT_CANCELLED)
                .anyMatch(PAYMENT_STATUS_IS_NOT_CREATED)) {
            throw new BusinessException(HttpStatus.BAD_REQUEST, ResponseMessage.INVALID_CANCEL_ITEM);
        }
    }

    //To check if there are more than 1 item that are not cancelled
    private boolean moreThanOneItem(RequisitionEntity requisitionEntity) {
        return requisitionEntity.getRequisitionItems()
                .stream()
                .filter(PAYMENT_STATUS_IS_NOT_CANCELLED)
                .count() > 1;
    }

    @Override
    public void utilizePayment(Long requisitionId, Long itemId, BigDecimal actualAmount) throws BusinessException {
        RequisitionEntity requisitionEntity = requisitionService.findRequisitionById(requisitionId);

        checkRequisitionStatusForUtilizePayment(requisitionEntity);

        //Check if item exist or overlimit
        itemExist(requisitionEntity, itemId);
        checkOverlimitItemPrice(requisitionEntity, itemId, actualAmount);

        itemRepository.utilizePaymentByRequisitionIdAndItemId(
                requisitionEntity.getRequisitionItems()
                        .stream()
                        .filter(i -> Objects.equals(i.getItemId(), itemId))
                        .mapToLong(RequisitionItemEntity::getId)
                        .iterator().nextLong(),
                requisitionId,
                itemId,
                actualAmount);

        if(requisitionEntity.getRequestStatus() != RequisitionStatus.IN_PROGRESS) {
            //Change requisition status to IN_PROGRESS
            requisitionService.logAndUpdateRequisitionStatus(
                    requisitionEntity,
                    RequisitionStatus.IN_PROGRESS,
                    requisitionEntity.getCreatedBy(),
                    PAYMENT_UTILIZATION);
        }
    }

    private void checkRequisitionStatusForUtilizePayment(RequisitionEntity requisitionEntity)throws BusinessException{
        if (!actualizationStatuses.contains(requisitionEntity.getRequestStatus())){
            throw new BusinessException(HttpStatus.BAD_REQUEST, ResponseMessage.STATUS_NOT_MATCH);
        }
    }

    public void itemExist(RequisitionEntity requisitionEntity, Long itemId) throws BusinessException {
        if(requisitionEntity.getRequisitionItems().stream().noneMatch(r -> r.getItemId().equals(itemId))) {
            log.error("Item not found!");
            throw new BusinessException(HttpStatus.BAD_REQUEST, ResponseMessage.ITEM_NOT_FOUND);
        }
    }

    public void checkOverlimitItemPrice(RequisitionEntity requisitionEntity, Long itemId, BigDecimal actualAmount) throws BusinessException {
        Optional<RequisitionItemEntity> optional =  requisitionEntity.getRequisitionItems().stream().filter(r->r.getItemId().equals(itemId)).findFirst();
        if(!optional.isPresent()) {
            log.error("Item not found!");
            throw new BusinessException(HttpStatus.BAD_REQUEST, ResponseMessage.ITEM_NOT_FOUND);
        }

        CatalogueEntity catalogueEntity = catalogueService.findCatalogueById(itemId);
        int itemQty = optional.get().getItemQty();
        BigDecimal itemBasePrice = catalogueEntity.getPrice();
        if (itemBasePrice.multiply(ItemMaxPriceLimitMultiply).multiply(new BigDecimal(itemQty)).compareTo(actualAmount) < 0){
            log.error("Item overprice!");
            throw new BusinessException(HttpStatus.BAD_REQUEST, ResponseMessage.ITEM_OVERPRICE_LIMIT_20);
        }
    }

    @Override
    public void uploadPayment(Long requisitionId, Long itemId, String invoiceId) {
        itemRepository.uploadPaymentByRequisitionIdAndItemId(requisitionId, itemId, invoiceId);
    }
    @Override
    public BigDecimal calculateActualization(RequisitionEntity requisitionEntity) {
        return requisitionEntity.getRequisitionItems().stream().parallel()
                .filter(e->e.getPaymentStatus().equals(PaymentStatus.ACTUALIZED))
                .map(RequisitionItemEntity::getActualAmount).reduce(BigDecimal.ZERO,BigDecimal::add);
    }



    @Override
    public RequisitionItemEntity findItemByInvoiceId(String invoiceId) throws BusinessException {
        return itemRepository.findItemByInvoiceId(invoiceId).orElseThrow(() -> new BusinessException(HttpStatus.NOT_FOUND, ResponseMessage.ITEM_NOT_FOUND));
    }

    @Override
    public void checkAndUpdatePaymentStatusByInvoiceId(String invoiceId) throws BusinessException {

        RequisitionItemEntity requisitionItemEntity = findItemByInvoiceId(invoiceId);

        if(requisitionItemEntity.getActualAmount().compareTo(BigDecimal.ZERO) < 1
                && requisitionItemEntity.getRequisitionItemInvoices().stream().allMatch(INVOICE_IS_DELETED)) {
            requisitionItemEntity.setPaymentStatus(PaymentStatus.CREATED);
        } else if(requisitionItemEntity.getActualAmount().compareTo(BigDecimal.ZERO) < 1
                || requisitionItemEntity.getRequisitionItemInvoices().stream().allMatch(INVOICE_IS_DELETED)) {
            requisitionItemEntity.setPaymentStatus(PaymentStatus.UTILIZED);
        } else if(requisitionItemEntity.getActualAmount().compareTo(BigDecimal.ZERO) > 0
                && requisitionItemEntity.getRequisitionItemInvoices().stream().anyMatch(INVOICE_IS_NOT_DELETED)) {
            requisitionItemEntity.setPaymentStatus(PaymentStatus.ACTUALIZED);
        }
        itemRepository.save(requisitionItemEntity);

    }

    @Override
    public RequisitionItemEntity findItemByRequisitionIdAndItemId(Long requisitionId, Long itemId) throws BusinessException {
        return itemRepository.findItemByRequisitionIdAndItemId(requisitionId, itemId).orElseThrow(() -> new BusinessException(HttpStatus.NOT_FOUND, ResponseMessage.ITEM_NOT_FOUND));
    }

    @Override
    public List<ItemPriceListDTO> getItemsByRequisitionIds(List<Long> requisitionIds) {
        return itemRepository.findItemListByRequisitionIds(requisitionIds).stream().map(i -> ItemPriceListDTO.builder()
                .id(i.getId())
                .price(i.getPrice())
                .build()).collect(Collectors.toList());
    }

    @Override
    public void reverseRequisitionItems(Long requisitionId) {
        itemRepository.reverseRequisitionItems(requisitionId);
    }

    @Override
    public void revertItemStatus(Set<RequisitionItemEntity> requisitionItems) {
        requisitionItems
                .forEach(i -> {
                    if(i.getActualAmount().compareTo(BigDecimal.ZERO) > 0 && !i.getRequisitionItemInvoices().isEmpty()) {
                        i.setPaymentStatus(PaymentStatus.ACTUALIZED);
                    } else if((i.getActualAmount().compareTo(BigDecimal.ZERO) > 0 && i.getRequisitionItemInvoices().isEmpty())
                            || (i.getActualAmount().compareTo(BigDecimal.ZERO) == 0 && !i.getRequisitionItemInvoices().isEmpty())) {
                        i.setPaymentStatus(PaymentStatus.UTILIZED);
                    } else if(i.getActualAmount().compareTo(BigDecimal.ZERO) == 0 && i.getRequisitionItemInvoices().isEmpty()) {
                        i.setPaymentStatus(PaymentStatus.CREATED);
                    }
                });
        itemRepository.saveAll(requisitionItems);
    }

    @Override
    public RequisitionEntity filterCancelledItems(RequisitionEntity requisitionEntity, long[] cancelledItemIds) {
        requisitionEntity.getRequisitionItems().forEach(i -> {
            if(Arrays.stream(cancelledItemIds).anyMatch(c -> c == i.getItemId())) {
                i.setPaymentStatus(PaymentStatus.CANCELLED);
            }
        });
        itemRepository.cancelItemsByRequisitionIdAndItemIds(requisitionEntity.getId(), cancelledItemIds);
        return requisitionEntity;
    }

    @Override
    public void updateItemPriceByRequisitionIds(String status, boolean all, List<Long> requisitionIds) {
        if(status != null && status.equalsIgnoreCase("active")) {
            itemRepository.updatePriceByRequestStatusActive();
        } else if(status != null && status.equalsIgnoreCase("completed")) {
            itemRepository.updatePriceByRequestStatusCompleted();
        } else if(status != null && status.equalsIgnoreCase("all")) {
            itemRepository.updateAllPrice();
        } else {
            List<RequisitionItemEntity> requisitionItemEntities;
            List<CatalogueEntity> catalogueEntities;

            if(all) {
                requisitionItemEntities = itemRepository.findAll();
                catalogueEntities = catalogueService.findAllCatalogues();
            } else {
                requisitionItemEntities = itemRepository.findItemListByRequisitionIds(requisitionIds);
                catalogueEntities = catalogueService.findCatalogueByIds(requisitionItemEntities.stream().map(RequisitionItemEntity::getItemId).collect(Collectors.toList()));
            }

            List<CatalogueEntity> finalCatalogueEntities = catalogueEntities;

            requisitionItemEntities.forEach(i -> {
                Optional<CatalogueEntity> catalogueEntity = finalCatalogueEntities.stream().filter(c -> c.getId().equals(i.getItemId())).findFirst();
                if (catalogueEntity.isPresent()) {
                    i.setPrice(catalogueEntity.get().getPrice());
                } else {
                    log.error("Item not found!");
                }
            });

            itemRepository.saveAll(requisitionItemEntities);
        }
    }

    @Override
    public void manualUpdateItemPriceByRequisitionIds(List<ItemPriceListDTO> itemPriceListDTOS) {
        List<RequisitionItemEntity> requisitionItemEntities = itemRepository.findByIds(itemPriceListDTOS.stream().map(ItemPriceListDTO::getId).collect(Collectors.toList()));
        requisitionItemEntities.forEach(i -> {
            Optional<ItemPriceListDTO> itemPriceListDTO = itemPriceListDTOS.stream().filter(p -> p.getId() == i.getId()).findFirst();
            itemPriceListDTO.ifPresent(priceListDTO -> i.setPrice(priceListDTO.getPrice()));
        });
        itemRepository.saveAll(requisitionItemEntities);
    }
}
