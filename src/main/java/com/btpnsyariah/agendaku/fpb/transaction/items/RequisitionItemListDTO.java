package com.btpnsyariah.agendaku.fpb.transaction.items;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Set;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class RequisitionItemListDTO {

    private Long itemId;
    private String itemName;
    private String itemCategory;
    private int itemQty;
    private String measurementUnit;
    private String itemLifeSpan;
    private BigDecimal price;
    private BigDecimal actualAmount;
    private PaymentStatus paymentStatus;
    private Set<String> invoiceId;

}
