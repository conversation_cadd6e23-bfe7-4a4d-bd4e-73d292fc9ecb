package com.btpnsyariah.agendaku.fpb.transaction.items;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

public interface ItemRepository extends JpaRepository<RequisitionItemEntity, Long> {

    @Query(nativeQuery = true, value = "SELECT\n" +
            "\tfri.Id,\n" +
            "\tfri.ItemId,\n" +
            "\tfic.ItemName,\n" +
            "\tfcc.CategoryName AS ItemCategory,\n" +
            "\tfri.ItemQty,\n" +
            "\tfic.MeasurementUnit,\n" +
            "\tfic.Lifespan AS ItemLifespan,\n" +
            "\tfri.Price,\n" +
            "\tfri.ActualAmount,\n" +
            "\tfri.PaymentStatus\n" +
            "FROM\n" +
            "\tFPBRequestItems fri\n" +
            "INNER JOIN FPBItemCatalogue fic\n" +
            "ON\n" +
            "\tfic.Id = fri.ItemId\n" +
            "INNER JOIN FPBCatalogueCategory fcc\n" +
            "ON\n" +
            "\tfcc.Id = fic.Category\n" +
            "WHERE\n" +
            "\tfri.RequisitionId = :requisitionId")
    List<RequisitionItemListProjection> findItemListByRequisitionId(@Param("requisitionId") Long requisitionId);

    @Transactional
    @Modifying
    @Query(nativeQuery = true, value = "UPDATE dbo.FPBRequestItems SET PaymentStatus = 'CANCELLED' WHERE RequisitionId = :requisitionId")
    void cancelItemsByRequisitionId(@Param("requisitionId") Long requisitionId);

    @Transactional
    @Modifying
    @Query(nativeQuery = true, value = "UPDATE dbo.FPBRequestItems SET PaymentStatus = 'CANCELLED' WHERE RequisitionId = :requisitionId AND ItemId = :itemId")
    void cancelItemByRequisitionIdAndItemId(@Param("requisitionId") Long requisitionId, @Param("itemId") Long itemId);

    @Transactional
    @Modifying
    @Query(nativeQuery = true, value = "UPDATE\n" +
            "\tdbo.FPBRequestItems\n" +
            "SET\n" +
            "\tActualAmount = :actualAmount,\n" +
            "\tPaymentStatus =\n" +
            "\t(CASE\n" +
            "\t\tWHEN EXISTS(SELECT TOP 1 * FROM FPBInvoice WHERE RequisitionItemId = :requisitionItemId AND IsDeleted = 0) THEN 'ACTUALIZED'\n" +
            "\t\tELSE 'UTILIZED'\n" +
            "\tEND)\n" +
            "WHERE\n" +
            "\t\tRequisitionId = :requisitionId\n" +
            "\tAND ItemId = :itemId")
    void utilizePaymentByRequisitionIdAndItemId(@Param("requisitionItemId") Long requisitionItemId,
                                                @Param("requisitionId") Long requisitionId,
                                                @Param("itemId") Long itemId,
                                                @Param("actualAmount") BigDecimal actualAmount);

    @Transactional
    @Modifying
    @Query(nativeQuery = true, value = "UPDATE\n" +
            "\tdbo.FPBRequestItems\n" +
            "SET\n" +
            "\tInvoiceId = :invoiceId,\n" +
            "\tPaymentStatus =\n" +
            "\t(CASE\n" +
            "\t\tWHEN ActualAmount IS NOT NULL AND ActualAmount != 0 THEN 'ACTUALIZED'\n" +
            "\t\tELSE 'UTILIZED'\n" +
            "\tEND)\n" +
            "WHERE\n" +
            "\t\tRequisitionId = :requisitionId\n" +
            "\tAND ItemId = :itemId")
    void uploadPaymentByRequisitionIdAndItemId(@Param("requisitionId") Long requisitionId,
                                                @Param("itemId") Long itemId,
                                                @Param("invoiceId") String invoiceId);

    @Query(nativeQuery = true, value = "SELECT * FROM FPBRequestItems WHERE Id = (SELECT RequisitionItemId FROM FPBInvoice WHERE InvoiceId = :invoiceId)")
    Optional<RequisitionItemEntity> findItemByInvoiceId(String invoiceId);

    @Query(nativeQuery = true, value = "SELECT * FROM FPBRequestItems fri WHERE fri.RequisitionId = :requisitionId AND fri.ItemId = :itemId")
    Optional<RequisitionItemEntity> findItemByRequisitionIdAndItemId(@Param("requisitionId") Long requisitionId, @Param("itemId") Long itemId);


    @Transactional
    @Modifying
    @Query(nativeQuery = true, value = "UPDATE\n" +
            "\tFPBRequestItems\n" +
            "SET\n" +
            "\tPaymentStatus = 'CREATED',\n" +
            "\tActualAmount = 0\n" +
            "WHERE\n" +
            "\tRequisitionId = :requisitionId")
    void reverseRequisitionItems(@Param("requisitionId") Long requisitionId);

    @Transactional
    @Modifying
    @Query(nativeQuery = true, value = "UPDATE\n" +
            "\tFPBRequestItems\n" +
            "SET\n" +
            "\tPaymentStatus = 'CANCELLED'\n" +
            "WHERE\n" +
            "\tRequisitionId = :requisitionId AND ItemId IN :cancelledItemIds")
    void cancelItemsByRequisitionIdAndItemIds(@Param("requisitionId") Long id, @Param("cancelledItemIds") long[] cancelledItemIds);

    @Query(nativeQuery = true, value = "SELECT * FROM FPBRequestItems WHERE RequisitionId IN :requisitionIds")
    List<RequisitionItemEntity> findItemListByRequisitionIds(List<Long> requisitionIds);

    @Query(nativeQuery = true, value = "SELECT * FROM FPBRequestItems WHERE Id IN :ids")
    List<RequisitionItemEntity> findByIds(List<Long> ids);

    @Query(nativeQuery = true, value = "SELECT * FROM FPBRequestItems fri INNER JOIN FPBRequest fr ON fri.RequisitionId = fr.Id WHERE fr.Request")
    List<RequisitionItemEntity> findAllActive();

    @Transactional
    @Modifying
    @Query(nativeQuery = true, value = "UPDATE\n" +
            "\tFPBRequestItems\n" +
            "SET\n" +
            "\tPrice =(\n" +
            "\tSELECT\n" +
            "\t\tfc.Price\n" +
            "\tFROM\n" +
            "\t\tFPBItemCatalogue fc\n" +
            "\tWHERE\n" +
            "\t\tfc.Id = FPBRequestItems.ItemId)\n" +
            "WHERE \n" +
            "\tId IN (\n" +
            "\tSELECT\n" +
            "\t\tfi.Id\n" +
            "\tFROM\n" +
            "\t\tFPBRequestItems fi\n" +
            "\tINNER JOIN FPBRequest fpbr ON\n" +
            "\t\tfpbr.Id = fi.RequisitionId\n" +
            "\tWHERE\n" +
            "\t\tfpbr.RequestStatus NOT IN('COMPLETED', 'CANCELLED'))")
    void updatePriceByRequestStatusActive();

    @Transactional
    @Modifying
    @Query(nativeQuery = true, value = "UPDATE\n" +
            "\tFPBRequestItems\n" +
            "SET\n" +
            "\tPrice =(\n" +
            "\tSELECT\n" +
            "\t\tfc.Price\n" +
            "\tFROM\n" +
            "\t\tFPBItemCatalogue fc\n" +
            "\tWHERE\n" +
            "\t\tfc.Id = FPBRequestItems.ItemId)\n" +
            "WHERE \n" +
            "\tId IN (\n" +
            "\tSELECT\n" +
            "\t\tfi.Id\n" +
            "\tFROM\n" +
            "\t\tFPBRequestItems fi\n" +
            "\tINNER JOIN FPBRequest fpbr ON\n" +
            "\t\tfpbr.Id = fi.RequisitionId\n" +
            "\tWHERE\n" +
            "\t\tfpbr.RequestStatus = 'COMPLETED')")
    void updatePriceByRequestStatusCompleted();

    @Transactional
    @Modifying
    @Query(nativeQuery = true, value = "UPDATE\n" +
            "\tFPBRequestItems\n" +
            "SET\n" +
            "\tPrice =(\n" +
            "\tSELECT\n" +
            "\t\tfc.Price\n" +
            "\tFROM\n" +
            "\t\tFPBItemCatalogue fc\n" +
            "\tWHERE\n" +
            "\t\tfc.Id = FPBRequestItems.ItemId)")
    void updateAllPrice();
}
