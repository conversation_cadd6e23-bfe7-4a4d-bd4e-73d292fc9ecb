package com.btpnsyariah.agendaku.fpb.transaction.requisition;

import java.math.BigDecimal;
import java.sql.Timestamp;  

public interface RequisitionListProjection {

    Long getId();
    String getRefNo();
    Timestamp getCreatedDate();
    Timestamp getSettlementDate();
    String getCreatedByNik();
    String getCreatedByName();
    BigDecimal getRequestAmount();
    BigDecimal getActualizedAmount();
    RequisitionStatus getRequestStatus();
    String getNote();
    RequisitionStatus getPreviousStatus();
    Timestamp getFundDisbursedDate();
    boolean getBaExist();


}
