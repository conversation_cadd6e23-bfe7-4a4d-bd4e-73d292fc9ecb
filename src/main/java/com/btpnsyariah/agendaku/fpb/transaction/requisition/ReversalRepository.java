package com.btpnsyariah.agendaku.fpb.transaction.requisition;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;

@Repository
public interface ReversalRepository extends JpaRepository<ReversalEntity, Long> {

    @Transactional
    @Modifying
    @Query(nativeQuery = true, value = "UPDATE\n" +
            "\tFPBReversal\n" +
            "SET\n" +
            "\tSettledAmount = :settledAmount\n" +
            "WHERE\n" +
            "\tId = (\n" +
            "\tSELECT\n" +
            "\t\tTOP 1 Id\n" +
            "\tFROM\n" +
            "\t\tFPBReversal\n" +
            "\tWHERE\n" +
            "\t\tRequisitionId = :requisitionId\n" +
            "\tORDER BY\n" +
            "\t\tCreatedDate DESC)")
    void updateSettledAmount(@Param("requisitionId") Long requisitionId, @Param("settledAmount") BigDecimal actualizedAmount);
}
