package com.btpnsyariah.agendaku.fpb.transaction.requisition;

import com.btpnsyariah.agendaku.fpb.catalogue.ItemLifespan;
import com.btpnsyariah.agendaku.fpb.document.DocumentService;
import com.btpnsyariah.agendaku.fpb.document.DocumentType;
import com.btpnsyariah.agendaku.fpb.exception.BusinessException;
import com.btpnsyariah.agendaku.fpb.model.*;
import com.btpnsyariah.agendaku.fpb.other.service.NationalHolidayService;
import com.btpnsyariah.agendaku.fpb.service.ExternalCommService;
import com.btpnsyariah.agendaku.fpb.service.NotificationConfigurator;
import com.btpnsyariah.agendaku.fpb.service.StorageService;
import com.btpnsyariah.agendaku.fpb.transaction.items.ItemService;
import com.btpnsyariah.agendaku.fpb.transaction.items.PaymentStatus;
import com.btpnsyariah.agendaku.fpb.transaction.items.RequisitionItemEntity;
import com.btpnsyariah.agendaku.fpb.transaction.log.TransactionLogEntity;
import com.btpnsyariah.agendaku.fpb.transaction.log.TransactionLogService;
import com.btpnsyariah.agendaku.fpb.transaction.xlink.XlinkService;
import com.btpnsyariah.agendaku.fpb.user.CashUserPersonaService;
import com.btpnsyariah.agendaku.fpb.util.Utility;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.text.WordUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

import static com.btpnsyariah.agendaku.fpb.model.Constant.*;
import static com.btpnsyariah.agendaku.fpb.model.Predicates.*;

@Component
@Slf4j
public class RequisitionServiceImpl implements RequisitionService{
    @Autowired
    RequisitionRepository requisitionRepository;
    @Autowired
    TransactionLogService transactionLogService;
    @Autowired
    NotificationConfigurator notificationConfigurator;
    @Autowired
    CashUserPersonaService cashUserPersonaService;
    @Autowired
    DocumentService documentService;
    @Lazy
    @Autowired
    ItemService itemService;
    @Autowired
    ReversalRepository reversalRepository;

    @Autowired
    FPBReversalItemRepository fpbReversalItemRepository;
    @Autowired
    StorageService storageService;
    @Autowired
    ExternalCommService externalCommService;
    @Autowired
    NationalHolidayService nationalHolidayService;
    @Autowired
    XlinkService xlinkService;

    private final List<RequisitionStatus> requisitionBaOverageStatuses = new ArrayList<>(List.of(
            RequisitionStatus.SETTLEMENT_APPROVED_BM,
            RequisitionStatus.SETTLEMENT_REJECTED_BM,
            RequisitionStatus.SETTLEMENT_REJECTED_PIC_KAS
    ));
    private final List<RequisitionStatus> requisitionOverageStatusExclusions = new ArrayList<>(List.of(
            RequisitionStatus.COMPLETED,
            RequisitionStatus.CANCELLED,
            RequisitionStatus.CANCEL_APPROVED_BM,
            RequisitionStatus.RV_REQUESTED,
            RequisitionStatus.REIMBURSEMENT,
            RequisitionStatus.COLLECTED_LB,
            RequisitionStatus.CANCEL_REQUESTED
    ));
    private final List<RequisitionStatus> requisitionReversibleStatuses = new ArrayList<>(List.of(
            RequisitionStatus.ACTUALIZED,
            RequisitionStatus.SETTLEMENT_APPROVED_BM,
            RequisitionStatus.SETTLEMENT_RV_REJECTED_BM,
            RequisitionStatus.REIMBURSEMENT,
            RequisitionStatus.COLLECTED_LB,
            RequisitionStatus.COMPLETED
    ));

    @Override
    public RequisitionStatusAndListDTO getRequisitions(String nik, String nikParam, boolean approval, boolean history, String mmsCode) throws BusinessException {
        if (nikParam != null && !nikParam.isBlank()){
            nik = nikParam;
        }
        RequisitionStatusAndListDTO dto = new RequisitionStatusAndListDTO();
        if(history) {
            if(approval) {
                dto.setList( requisitionRepository.findHistoryByMmsCodeForList(mmsCode).stream().map(r -> new RequisitionListDTO(
                        r.getId(),
                        r.getRefNo(),
                        r.getCreatedDate(),
                        r.getSettlementDate(),
                        r.getCreatedByNik(),
                        r.getCreatedByName() == null?r.getCreatedByNik():r.getCreatedByName(),
                        r.getRequestAmount(),
                        r.getActualizedAmount(),
                        r.getRequestStatus(),
                        r.getNote())
                ).collect(Collectors.toList()));
            } else {
                dto.setList( requisitionRepository.findHistoryByNik(nik).stream().map(r -> new RequisitionListDTO(
                                r.getId(),
                                r.getRefNo(),
                                r.getCreatedDate(),
                                r.getSettlementDate(),
                                r.getCreatedByNik(),
                                r.getCreatedByName() == null?r.getCreatedByNik():r.getCreatedByName(),
                                r.getRequestAmount(),
                                r.getActualizedAmount(),
                                r.getRequestStatus(),
                                r.getNote(),
                                r.getPreviousStatus()
                        )
                ).collect(Collectors.toList()));
            }
        } else {
            if(approval) {
                dto.setList( requisitionRepository.findByMmsCodeForList(mmsCode).stream().map(r -> {

                    int transactionAge = r.getFundDisbursedDate() != null ? (int) Utility.calculateDateDiffExcludeWeekendAndNationalHolidays(r.getFundDisbursedDate(), new Date(), nationalHolidayService.getAllNationalHolidaysStartingIn(r.getFundDisbursedDate(), new Date())) : 0;
                    return RequisitionListDTO.builder()
                            .requestId(r.getId())
                            .refNo(r.getRefNo())
                            .createdDate(r.getCreatedDate())
                            .settlementDate(r.getSettlementDate())
                            .createdByNik(r.getCreatedByNik())
                            .createdByName(r.getCreatedByName() == null?r.getCreatedByNik():r.getCreatedByName())
                            .requestAmount(r.getRequestAmount())
                            .actualizedAmount(r.getActualizedAmount())
                            .requestStatus(r.getRequestStatus())
                            .previousStatus(RequisitionStatus.ACTUALIZE_ITEM_BOY.equals(r.getPreviousStatus()) || RequisitionStatus.ACTUALIZE_ITEM_AOY.equals(r.getPreviousStatus()) ? RequisitionStatus.COMPLETED : r.getPreviousStatus())
                            .note(r.getNote())
                            .overaging(!r.getBaExist() &&
                                    transactionAge > 10 &&
                                    !requisitionOverageStatusExclusions.contains(r.getRequestStatus()) ?
                                    Math.abs(10 - transactionAge) : 0)
                            .baExist(r.getBaExist())
                            .build();

                }).collect(Collectors.toList()));
            } else {
                dto.setList( requisitionRepository.findByNik(nik).stream().map(r -> {

                    int transactionAge = r.getFundDisbursedDate() != null ? (int) Utility.calculateDateDiffExcludeWeekendAndNationalHolidays(r.getFundDisbursedDate(), new Date(), nationalHolidayService.getAllNationalHolidaysStartingIn(r.getFundDisbursedDate(), new Date())) : 0;
                    return RequisitionListDTO.builder()
                            .requestId(r.getId())
                            .refNo(r.getRefNo())
                            .createdDate(r.getCreatedDate())
                            .settlementDate(r.getSettlementDate())
                            .createdByNik(r.getCreatedByNik())
                            .createdByName(r.getCreatedByName())
                            .requestAmount(r.getRequestAmount())
                            .actualizedAmount(r.getActualizedAmount())
                            .requestStatus(r.getRequestStatus())
                            .previousStatus(RequisitionStatus.ACTUALIZE_ITEM_BOY.equals(r.getPreviousStatus()) || RequisitionStatus.ACTUALIZE_ITEM_AOY.equals(r.getPreviousStatus()) ? RequisitionStatus.COMPLETED : r.getPreviousStatus())
                            .note(r.getNote())
                            .overaging(!r.getBaExist() &&
                                    transactionAge > 10 &&
                                    !requisitionOverageStatusExclusions.contains(r.getRequestStatus()) ?
                                    Math.abs(10 - transactionAge) : 0)
                            .baExist(r.getBaExist())
                            .build();

                }).collect(Collectors.toList()));
            }
        }
        return setRequisitionCompletion(dto, mmsCode);
    }

    private RequisitionStatusAndListDTO setRequisitionCompletion(RequisitionStatusAndListDTO dto, String mmsCode) throws BusinessException {
        Optional<RequisitionEntity> ongoingRequisition = checkIsRequisitionNotCompleted(mmsCode);
        if (ongoingRequisition.isPresent()){
            String name="";
            try {
                CashUserPersonasEntity cashUserPersonasEntity = cashUserPersonaService.findByNik(ongoingRequisition.get().getCreatedBy());
                name = WordUtils.capitalizeFully(cashUserPersonasEntity.getCoName());
            }catch (Exception e) {
                name = ongoingRequisition.get().getCreatedBy();
            }

            dto.setRequisitionNotCompleted(true);
            dto.setRequisitionIsNotCompletedMessage(String.format(ResponseMessage.REQUISITION_NOT_COMPLETED,
                    name,
                    ongoingRequisition.get().getRequisitionNo(),
                    Utility.getIndonesianCurrencyValue(ongoingRequisition.get().getRequestAmount())));
        } else {
            dto.setRequisitionNotCompleted(false);
            dto.setRequisitionIsNotCompletedMessage("");
        }
        return dto;
    }

    @Override
    public String submitRequisition(String userId, String mmsCode, RequisitionEntity requisitionEntity) throws BusinessException {
        RequisitionEntity requisition = validateSubmitRequest(userId, mmsCode, requisitionEntity);
        requisitionEntity.getRequisitionItems().forEach(r -> r.setRequisitionEntity(requisition));
        return logAndUpdateRequisition(requisitionRepository.save(requisition));
    }

    private RequisitionEntity validateSubmitRequest(String userId, String mmsCode, RequisitionEntity requisitionEntity) throws BusinessException {

        if(userId == null || userId.isBlank()) {
            throw new BusinessException(HttpStatus.BAD_REQUEST, ResponseMessage.REQUISITION_MISSING_DATA);
        }

        if((mmsCode == null || mmsCode.isBlank()) || (requisitionEntity.getMmsCode() == null || requisitionEntity.getMmsCode().isBlank())) {
            throw new BusinessException(HttpStatus.BAD_REQUEST, ResponseMessage.REQUISITION_MISSING_DATA);
        }

        checkOngoingRequisition(requisitionEntity.getMmsCode());

        return requisitionEntity;
    }

    private void checkOngoingRequisition(String mmsCode) throws BusinessException {
        Optional<RequisitionEntity> ongoingRequisition = checkIsRequisitionNotCompleted(mmsCode);
        if (ongoingRequisition.isPresent()){
            CashUserPersonasEntity cashUserPersonasEntity = cashUserPersonaService.findByNik(ongoingRequisition.get().getCreatedBy());
            throw new BusinessException(HttpStatus.BAD_REQUEST, String.format(ResponseMessage.REQUISITION_NOT_COMPLETED,
                    WordUtils.capitalizeFully(cashUserPersonasEntity.getCoName()),
                    ongoingRequisition.get().getRequisitionNo(),
                    Utility.getIndonesianCurrencyValue(ongoingRequisition.get().getRequestAmount())));
        }
    }

    private String logAndUpdateRequisition(RequisitionEntity requisitionEntity) throws BusinessException {

        //Find requisition to get the number
        RequisitionListProjection savedRequisition = requisitionRepository.findByMmsCode(requisitionEntity.getMmsCode())
                .stream()
                .filter(r -> Objects.equals(r.getId(), requisitionEntity.getId()))
                .collect(Collectors.toList()).get(0);

        //Format to roman numeral, update the requisition data then log it
        String requisitionNumber = Utility.formatRequisitionNumber(savedRequisition.getRefNo());
        requisitionRepository.updateRequisitionNumber(requisitionNumber, savedRequisition.getId());
        transactionLogService.logRequisition(requisitionEntity);

        //Push approval notification
        notificationConfigurator.pushApprovalRequestNotification(
                requisitionEntity.getMmsCode(),
                requisitionEntity.getCreatedBy(),
                Utility.getIndonesianCurrencyValue(requisitionEntity.getRequestAmount()),
                NotificationType.FPB_REQUEST
        );

        return requisitionNumber;
    }

    private Optional<RequisitionEntity> checkIsRequisitionNotCompleted(String mmsCode){
        return requisitionRepository.existsByMmsCodeAndRequestStatusIn(mmsCode);
    }

    @Override
    public void submitApprovalRequisitions(RequisitionApprovalDTO requisitionApprovalDTO, String userId, String accessToken, String coCode, String rrn,String deviceId) throws  BusinessException {

        RequisitionEntity requisitionEntity = requisitionRepository.findById(requisitionApprovalDTO.getRequestId())
                        .orElseThrow(() -> new BusinessException(HttpStatus.NOT_FOUND, ResponseMessage.REQUISITION_NOT_FOUND));

        switch (requisitionApprovalDTO.getRequestStatus()){
            case REQUEST_APPROVED_BM:

                cashUserPersonaService.validateUserRole(userId, PersonaRole.BM);
                requisitionStatusCheck(requisitionEntity.getRequestStatus(), RequisitionStatus.SUBMITTED);

                logAndUpdateRequisitionStatus(
                        requisitionEntity,
                        RequisitionStatus.REQUEST_APPROVED_BM,
                        userId,
                        requisitionApprovalDTO.getNote());

                //Push approval request notification to PIC KW
                notificationConfigurator.pushApprovalRequestNotification(
                        requisitionEntity.getMmsCode(),
                        requisitionEntity.getCreatedBy(),
                        Utility.getIndonesianCurrencyValue(requisitionEntity.getRequestAmount()),
                        NotificationType.FPB_REQUEST_APPROVED_BM
                );
                break;
            case REQUEST_APPROVED_PIC_KAS:

                cashUserPersonaService.validateUserRole(userId, PersonaRole.KW);
                requisitionStatusCheck(requisitionEntity.getRequestStatus(), RequisitionStatus.REQUEST_APPROVED_BM);

                logAndUpdateRequisitionStatus(
                        requisitionEntity,
                        RequisitionStatus.REQUEST_APPROVED_PIC_KAS,
                        userId,
                        requisitionApprovalDTO.getNote());

                //Push approval response notification
                notificationConfigurator.pushApprovalResponseNotification(
                        requisitionEntity.getCreatedBy(),
                        NotificationType.FPB_REQUEST_APPROVED_KW
                );
                break;
            case REQUEST_REJECTED_BM:

                cashUserPersonaService.validateUserRole(userId, PersonaRole.BM);
                requisitionStatusCheck(requisitionEntity.getRequestStatus(), RequisitionStatus.SUBMITTED);

                logAndUpdateRequisitionStatus(
                        requisitionEntity,
                        RequisitionStatus.REQUEST_REJECTED_BM,
                        userId,
                        requisitionApprovalDTO.getNote());

                //Push approval response notification
                notificationConfigurator.pushApprovalResponseNotification(
                        requisitionEntity.getCreatedBy(),
                        NotificationType.FPB_REQUEST_REJECTED_BM
                );
                break;
            case REQUEST_REJECTED_PIC_KAS:

                cashUserPersonaService.validateUserRole(userId, PersonaRole.KW);
                requisitionStatusCheck(requisitionEntity.getRequestStatus(), RequisitionStatus.REQUEST_APPROVED_BM);

                logAndUpdateRequisitionStatus(
                        requisitionEntity,
                        RequisitionStatus.REQUEST_REJECTED_PIC_KAS,
                        userId,
                        requisitionApprovalDTO.getNote());

                //Push approval response notification
                notificationConfigurator.pushApprovalResponseNotification(
                        requisitionEntity.getCreatedBy(),
                        NotificationType.FPB_REQUEST_REJECTED_KW
                );
                break;
            case SETTLEMENT_APPROVED_BM:

                cashUserPersonaService.validateUserRole(userId, PersonaRole.BM);
                requisitionStatusCheck(requisitionEntity.getRequestStatus(), RequisitionStatus.ACTUALIZED);

                logAndUpdateRequisitionStatus(
                        requisitionEntity,
                        RequisitionStatus.SETTLEMENT_APPROVED_BM,
                        userId,
                        requisitionApprovalDTO.getNote());

                //Push approval request notification
                notificationConfigurator.pushApprovalRequestNotification(
                        requisitionEntity.getMmsCode(),
                        requisitionEntity.getCreatedBy(),
                        Utility.getIndonesianCurrencyValue(requisitionEntity.getActualizedAmount()),
                        NotificationType.FPB_SETTLEMENT_APPROVED_BM
                );
                break;
            case SETTLEMENT_APPROVED_PIC_KAS:

                cashUserPersonaService.validateUserRole(userId, PersonaRole.KW);
                requisitionStatusCheck(requisitionEntity.getRequestStatus(), RequisitionStatus.SETTLEMENT_APPROVED_BM);
                settleRequisition(requisitionEntity, userId,deviceId);

                break;
            case SETTLEMENT_REJECTED_BM:

                cashUserPersonaService.validateUserRole(userId, PersonaRole.BM);
                requisitionStatusCheck(requisitionEntity.getRequestStatus(), RequisitionStatus.ACTUALIZED);

                logAndUpdateRequisitionStatus(
                        requisitionEntity,
                        RequisitionStatus.SETTLEMENT_REJECTED_BM,
                        userId,
                        requisitionApprovalDTO.getNote());

                //Push approval response notification
                notificationConfigurator.pushApprovalResponseNotification(
                        requisitionEntity.getCreatedBy(),
                        NotificationType.FPB_SETTLEMENT_REJECTED_BM
                );
                break;
            case SETTLEMENT_REJECTED_PIC_KAS:

                cashUserPersonaService.validateUserRole(userId, PersonaRole.KW);
                requisitionStatusCheck(requisitionEntity.getRequestStatus(), RequisitionStatus.SETTLEMENT_APPROVED_BM);

                logAndUpdateRequisitionStatus(
                        requisitionEntity,
                        RequisitionStatus.SETTLEMENT_REJECTED_PIC_KAS,
                        userId,
                        requisitionApprovalDTO.getNote());

                //Push approval response notification
                notificationConfigurator.pushApprovalResponseNotification(
                        requisitionEntity.getCreatedBy(),
                        NotificationType.FPB_SETTLEMENT_REJECTED_KW
                );
                break;
            case RV_APPROVED_BM:

                cashUserPersonaService.validateUserRole(userId, PersonaRole.BM);
                requisitionStatusCheck(requisitionEntity.getRequestStatus(), RequisitionStatus.RV_REQUESTED);
                reverseRequisition(
                        requisitionEntity.getId(),
                        userId,
                        false,
                        accessToken,
                        coCode,
                        rrn);

                break;
            case SETTLEMENT_RV_APPROVED_BM:

                cashUserPersonaService.validateUserRole(userId, PersonaRole.BM);
                requisitionStatusCheck(requisitionEntity.getRequestStatus(), RequisitionStatus.RV_REQUESTED);
                reverseRequisition(
                        requisitionEntity.getId(),
                        userId,
                        true,
                        accessToken,
                        coCode,
                        rrn);

                break;
            case SETTLEMENT_RV_REJECTED_BM:

                cashUserPersonaService.validateUserRole(userId, PersonaRole.BM);
                requisitionStatusCheck(requisitionEntity.getRequestStatus(), RequisitionStatus.RV_REQUESTED);

                logAndUpdateRequisitionStatus(
                        requisitionEntity,
                        RequisitionStatus.SETTLEMENT_RV_REJECTED_BM,
                        userId,
                        requisitionApprovalDTO.getNote());

                //Push approval response notification
                notificationConfigurator.pushApprovalResponseNotification(
                        requisitionEntity.getCreatedBy(),
                        NotificationType.FPB_REVERSAL_REJECTED_BM
                );
                break;
            case RV_REJECTED_BM:

                cashUserPersonaService.validateUserRole(userId, PersonaRole.BM);
                requisitionStatusCheck(requisitionEntity.getRequestStatus(), RequisitionStatus.RV_REQUESTED);

                logAndUpdateRequisitionStatus(
                        requisitionEntity,
                        RequisitionStatus.RV_REJECTED_BM,
                        userId,
                        requisitionApprovalDTO.getNote());

                //Push approval response notification
                notificationConfigurator.pushApprovalResponseNotification(
                        requisitionEntity.getCreatedBy(),
                        NotificationType.FPB_REVERSAL_REJECTED_BM
                );
                break;

            case CANCEL_APPROVED_BM:

                cashUserPersonaService.validateUserRole(userId, PersonaRole.BM);
                requisitionStatusCheck(requisitionEntity.getRequestStatus(), RequisitionStatus.CANCEL_REQUESTED);
                cancelRequisition(requisitionEntity, userId);

                break;
            case CANCEL_REJECTED_BM:

                cashUserPersonaService.validateUserRole(userId, PersonaRole.BM);
                requisitionStatusCheck(requisitionEntity.getRequestStatus(), RequisitionStatus.CANCEL_REQUESTED);

                logAndUpdateRequisitionStatus(
                        requisitionEntity,
                        RequisitionStatus.CANCEL_REJECTED_BM,
                        userId,
                        requisitionApprovalDTO.getNote());

                //Push approval response notification
                notificationConfigurator.pushApprovalResponseNotification(
                        requisitionEntity.getCreatedBy(),
                        NotificationType.FPB_CANCEL_REJECTED_BM
                );
                break;
            default:
                throw new BusinessException(HttpStatus.BAD_REQUEST, ResponseMessage.INVALID_PARAM);
        }
    }

    private void cancelRequisition(RequisitionEntity requisitionEntity, String userId) throws BusinessException {
        if(transactionLogService.checkRequisitionTransactionLogStatus(requisitionEntity.getId(), RequisitionStatus.DISBURSED_LB.toString())) {
            logAndUpdateRequisitionStatus(
                    requisitionEntity,
                    RequisitionStatus.CANCEL_APPROVED_BM,
                    userId,
                    REQUISITION_CANCEL_APPROVED_BM);

            //Push approval response notification
            notificationConfigurator.pushApprovalResponseNotification(
                    requisitionEntity.getCreatedBy(),
                    NotificationType.FPB_CANCEL_APPROVED_BM);
        } else {
            logAndUpdateRequisitionStatus(
                    requisitionEntity,
                    RequisitionStatus.CANCELLED,
                    userId,
                    REQUISITION_CANCELLED);

            //Push approval response notification
            notificationConfigurator.pushApprovalResponseNotification(
                    requisitionEntity.getCreatedBy(),
                    NotificationType.FPB_CANCEL_APPROVED_BM);
        }
    }

    private void settleRequisition(RequisitionEntity requisitionEntity, String userId,String deviceId) throws BusinessException {
        logAndUpdateRequisitionStatus(
                requisitionEntity,
                RequisitionStatus.SETTLEMENT_APPROVED_PIC_KAS,
                userId,
                TRANSACTION_SETTLED);

        if(requisitionEntity.getRequestAmount().compareTo(requisitionEntity.getActualizedAmount()) < 0) {
            logAndUpdateRequisitionStatus(
                    requisitionEntity,
                    RequisitionStatus.REIMBURSEMENT,
                    userId,
                    TRANSACTION_SETTLED);

            isReversed(requisitionEntity);

            //Push approval response notification
            notificationConfigurator.pushApprovalResponseNotification(
                    requisitionEntity.getCreatedBy(),
                    NotificationType.FPB_SETTLEMENT_APPROVED_KW_REIMBURSE
            );
        } else if(requisitionEntity.getRequestAmount().compareTo(requisitionEntity.getActualizedAmount()) > 0){
            logAndUpdateRequisitionStatus(
                    requisitionEntity,
                    RequisitionStatus.COLLECTED_LB,
                    userId,
                    TRANSACTION_SETTLED);

            isReversed(requisitionEntity);

            //Push approval response notification
            notificationConfigurator.pushApprovalResponseNotification(
                    requisitionEntity.getCreatedBy(),
                    NotificationType.FPB_SETTLEMENT_APPROVED_KW_SURPLUS
            );
        } else {
            logAndUpdateRequisitionStatus(
                    requisitionEntity,
                    RequisitionStatus.COMPLETED,
                    userId,
                    TRANSACTION_SETTLED);

            isReversed(requisitionEntity);

            setFPBItemXlinkAndLog(requisitionEntity,userId,deviceId);

            //Push approval response notification
            notificationConfigurator.pushApprovalResponseNotification(
                    requisitionEntity.getCreatedBy(),
                    NotificationType.FPB_SETTLEMENT_APPROVED_KW
            );

            notificationConfigurator.pushKfoDocNotification(
                    requisitionEntity.getMmsCode(),
                    NotificationType.FPB_DOC_COMPLETED
            );
        }
    }

    private void setFPBItemXlinkAndLog(RequisitionEntity requisitionEntity, String userId,String deviceId) throws BusinessException {
        BigDecimal itemAoyAmount = calculateItemTotalAmountByLifespan(requisitionEntity, RequestedType.FPB_ITEM_AOY);
        if(itemAoyAmount.compareTo(BigDecimal.ZERO) > 0) {
            xlinkService.saveXlinklog(requisitionEntity, RequestedType.FPB_ITEM_AOY, itemAoyAmount, userId, deviceId);
            transactionLogService.logRequisition(requisitionEntity.getId(), RequisitionStatus.ACTUALIZE_ITEM_AOY, userId, "", itemAoyAmount);
        }

        BigDecimal itemBoyAmount = calculateItemTotalAmountByLifespan(requisitionEntity, RequestedType.FPB_ITEM_BOY);
        if(itemBoyAmount.compareTo(BigDecimal.ZERO) > 0){
            xlinkService.saveXlinklog(requisitionEntity,RequestedType.FPB_ITEM_BOY,itemBoyAmount,userId,deviceId);
            transactionLogService.logRequisition(requisitionEntity.getId(),RequisitionStatus.ACTUALIZE_ITEM_BOY,userId,"",itemBoyAmount);
        }


        requisitionEntity.setActualizationAutopost(true);
        requisitionRepository.save(requisitionEntity);
    }

    //To check if transaction is reversed, if it is, this function will update the settled amount
    private void isReversed(RequisitionEntity requisitionEntity) {
        if(requisitionEntity.isReversed()) {
            reversalRepository.updateSettledAmount(requisitionEntity.getId(), requisitionEntity.getActualizedAmount());
        }
    }

    //Update requisition status then log it
    public void logAndUpdateRequisitionStatus(
            RequisitionEntity requisitionEntity,
            RequisitionStatus requisitionStatus,
            String userId,
            String note) throws BusinessException {

        if(requisitionStatus.equals(RequisitionStatus.RV_REJECTED_BM)) {
            requisitionEntity.setRequestStatus(RequisitionStatus.COMPLETED);
            requisitionRepository.save(requisitionEntity);
            transactionLogService.logRequisition(requisitionEntity.getId(), RequisitionStatus.RV_REJECTED_BM, userId, note);
        } else if(requisitionStatus.equals(RequisitionStatus.RV_APPROVED_BM)
                || requisitionStatus.equals(RequisitionStatus.RV_REIMBURSEMENT)
                || requisitionStatus.equals(RequisitionStatus.RV_COLLECTED_LB)) {
            requisitionEntity.setRequestStatus(requisitionStatus);
            requisitionRepository.save(requisitionEntity);

            TransactionLogEntity logEntity = transactionLogService.logRequisition(requisitionEntity.getId(), requisitionStatus, userId, note);
            ReversalEntity reversalEntity =  reversalRepository.save(
                    ReversalEntity.builder()
                            .requisitionId(requisitionEntity.getId())
                            .createdDate(logEntity.getCreatedDate())
                            .actualizedmount(requisitionEntity.getActualizedAmount())
                            .createdBy(requisitionEntity.getCreatedBy())
                            .approvalLog(logEntity.getId())
                            .build()
            );

            fpbReversalItemRepository.saveAll(mapRequisitionItemsToFPBReversalItems(requisitionEntity,reversalEntity));

            documentService.saveReverseFPBDocument(
                    requisitionEntity,
                    reversalEntity.getId(),
                    DocumentType.REVERSE_FPB,
                    userId
            );
        } else if(requisitionStatus.equals(RequisitionStatus.CANCEL_REJECTED_BM) || requisitionStatus.equals(RequisitionStatus.SETTLEMENT_RV_REJECTED_BM)) {
            RequisitionStatus previousStatus = requisitionRepository.findPreviousStatus(requisitionEntity.getId());
            boolean isSettled = transactionLogService.checkRequisitionTransactionLogStatus(requisitionEntity.getId(), RequisitionStatus.COMPLETED.toString());

            //Log the rejected cancellation or reversal
            transactionLogService.logRequisition(requisitionEntity.getId(), requisitionStatus, userId, note);

            //Revert and log requisition status
            if(requisitionStatus.equals(RequisitionStatus.SETTLEMENT_RV_REJECTED_BM)) {
                requisitionEntity.setRequestStatus(!previousStatus.equals(RequisitionStatus.ACTUALIZED) && isSettled ? RequisitionStatus.COMPLETED : RequisitionStatus.ACTUALIZED);
            } else {
                requisitionEntity.setRequestStatus(previousStatus);
            }

            requisitionRepository.save(requisitionEntity);
            transactionLogService.logRequisition(requisitionEntity.getId(), previousStatus, userId, note);

            //Revert items payment status
            itemService.revertItemStatus(requisitionEntity.getRequisitionItems());
        } else if(requisitionStatus.equals(RequisitionStatus.SETTLEMENT_REJECTED_BM) || requisitionStatus.equals(RequisitionStatus.SETTLEMENT_REJECTED_PIC_KAS)) {
            requisitionEntity.setRequestStatus(requisitionStatus);
            requisitionRepository.save(requisitionEntity);
            transactionLogService.logRequisition(requisitionEntity.getId(), requisitionStatus, userId, note);

            //Revert items payment status
            itemService.revertItemStatus(requisitionEntity.getRequisitionItems());
        } else {
            requisitionEntity.setRequestStatus(requisitionStatus);
            requisitionRepository.save(requisitionEntity);

            if(requisitionStatus.equals(RequisitionStatus.COLLECTED_LB) || requisitionStatus.equals(RequisitionStatus.REIMBURSEMENT)) {
                transactionLogService.logRequisition(
                        requisitionEntity.getId(),
                        requisitionStatus,
                        userId,
                        note,
                        requisitionEntity.getActualizedAmount().subtract(requisitionEntity.getRequestAmount()).abs());
            } else {
                transactionLogService.logRequisition(requisitionEntity.getId(), requisitionStatus, userId, note);
            }
        }
    }

    //To validate requisition status
    private void requisitionStatusCheck(RequisitionStatus requestStatus, RequisitionStatus comparingStatus) throws BusinessException {
        if (!requestStatus.equals(comparingStatus)){
            throw new BusinessException(HttpStatus.BAD_REQUEST, ResponseMessage.STATUS_NOT_MATCH);
        }
    }

    //To validate requisition status against several statuses
    private void requisitionStatusCheck(RequisitionStatus requestStatus, List<RequisitionStatus> comparingStatuses) throws BusinessException {
        if (!comparingStatuses.contains(requestStatus)){
            throw new BusinessException(HttpStatus.BAD_REQUEST, ResponseMessage.STATUS_NOT_MATCH);
        }
    }

    @Override
    public void validateRequisition(Long requisitionId) throws BusinessException {
        requisitionRepository.findById(requisitionId).orElseThrow(() -> new BusinessException(HttpStatus.NOT_FOUND, ResponseMessage.REQUISITION_NOT_FOUND));
    }

    @Override
    public RequisitionEntity findRequisitionById(Long requisitionId) throws BusinessException {
        return requisitionRepository.findById(requisitionId).orElseThrow(() -> new BusinessException(HttpStatus.NOT_FOUND, ResponseMessage.REQUISITION_NOT_FOUND));
    }

    @Override
    public void submitRequisitionSettlement(Long requisitionId, String userId, long[] cancelledItemIds) throws BusinessException {

        RequisitionEntity requisitionEntity = itemService.filterCancelledItems(findRequisitionById(requisitionId), cancelledItemIds);
        requisitionStatusCheck(requisitionEntity.getRequestStatus(), RequisitionStatus.IN_PROGRESS);
        if(cashUserPersonaService.validateUserPrivileges(userId, requisitionEntity.getCreatedBy())) {

            //Validate payment of all items
            validatePaymentStatus(requisitionEntity);

            //Calculate actualized items and set the total amount
            requisitionEntity.setActualizedAmount(itemService.calculateActualization(requisitionEntity));

            logAndUpdateRequisitionStatus(
                    requisitionRepository.save(requisitionEntity),
                    RequisitionStatus.ACTUALIZED,
                    userId,
                    SETTLEMENT_REQUEST
            );

            notificationConfigurator.pushApprovalRequestNotification(
                    requisitionEntity.getMmsCode(),
                    requisitionEntity.getCreatedBy(),
                    Utility.getIndonesianCurrencyValue(requisitionEntity.getActualizedAmount()),
                    NotificationType.FPB_SETTLEMENT_REQUEST
            );
        } else {
            throw new BusinessException(HttpStatus.BAD_REQUEST, ResponseMessage.UNAUTHORIZED_ACCESS);
        }
    }

    @Override
    public void requestCancelRequisition(Long requisitionId, String userId, String note) throws BusinessException {
        RequisitionEntity requisitionEntity = findRequisitionById(requisitionId);
        if(cashUserPersonaService.validateUserPrivileges(userId, requisitionEntity.getCreatedBy())) {
            logAndUpdateRequisitionStatus(
                    requisitionEntity,
                    RequisitionStatus.CANCEL_REQUESTED,
                    userId,
                    note != null && !note.isBlank() ? note : REQUISITION_CANCELLATION
            );
        } else {
            throw new BusinessException(HttpStatus.BAD_REQUEST, ResponseMessage.UNAUTHORIZED_ACCESS);
        }
    }

    @Override
    public void requestReverseRequisition(Long requisitionId, boolean settlement, String userId, String note) throws BusinessException {
        RequisitionEntity requisitionEntity = findRequisitionById(requisitionId);

        if(!settlement) {
            checkOngoingRequisition(requisitionEntity.getMmsCode());
        }

        requisitionStatusCheck(requisitionEntity.getRequestStatus(), requisitionReversibleStatuses);

        if(cashUserPersonaService.validateUserPrivileges(userId, requisitionEntity.getCreatedBy())) {

            logAndUpdateRequisitionStatus(
                    requisitionEntity,
                    RequisitionStatus.RV_REQUESTED,
                    userId,
                    note != null && !note.isBlank() ? note : REQUISITION_REVERSAL
            );

            notificationConfigurator.pushApprovalRequestNotification(
                    requisitionEntity.getMmsCode(),
                    requisitionEntity.getCreatedBy(),
                    Utility.getIndonesianCurrencyValue(requisitionEntity.getActualizedAmount()),
                    NotificationType.FPB_REVERSAL_REQUEST
            );
        }

    }

    @Override
    public ResponseEntity<BaseResponse> uploadBeritaAcara(Long requisitionId, String token, MultipartFile beritaAcara) throws BusinessException, IOException {

        RequisitionEntity requisitionEntity = findRequisitionById(requisitionId);
        String beritaAcaraId = BA_FILENAME_PREFIX + Utility.getHexValue(requisitionId.toString());

        ResponseEntity response = storageService.send(
                beritaAcara,
                token,
                beritaAcaraId,
                HttpMethod.PUT,
                MediaType.IMAGE_JPEG);

        if(response.getStatusCode() == HttpStatus.OK || response.getStatusCode() == HttpStatus.NO_CONTENT) {
            documentService.saveFPBDocument(requisitionEntity.getRequestStatus(), requisitionEntity, DocumentType.FPB_BA);
            if(requisitionBaOverageStatuses.contains(requisitionEntity.getRequestStatus())) {
                logAndUpdateRequisitionStatus(
                        requisitionEntity,
                        RequisitionStatus.IN_PROGRESS,
                        requisitionEntity.getCreatedBy(),
                        REQUISITION_UPLOAD_BA_RESET);
            }
            return ResponseEntity.ok(new BaseResponse(true, ResponseMessage.BA_UPLOADED));
        } else {
            String responseBody = response.getBody() != null ? response.getBody().toString() : "N/A";
            return new ResponseEntity<>(new BaseResponse(false, String.format(ResponseMessage.BA_UPLOAD_FAIL, responseBody)), response.getStatusCode());
        }
    }

    @Override
    public ResponseEntity getBeritaAcara(Long requisitionId, String token) {

        String beritaAcaraId = BA_FILENAME_PREFIX + Utility.getHexValue(requisitionId.toString());
        ResponseEntity response = storageService.send(
                token,
                beritaAcaraId,
                HttpMethod.GET);

        if(response.getStatusCode() == HttpStatus.OK || response.getStatusCode() == HttpStatus.NO_CONTENT) {
            return response;
        } else {
            String responseBody = response.getBody() != null ? response.getBody().toString() : "N/A";
            return new ResponseEntity<>(new BaseResponse(false, String.format(ResponseMessage.BA_GET_FAIL, responseBody)), response.getStatusCode());
        }
    }

    @Override
    public boolean getBeritaAcaraStatus(Long requisitionId) {
        return requisitionRepository.getBeritaAcaraStatus(requisitionId);
    }

    @Override
    public void changePic(Long requisitionId, String nikPic, String userId) throws BusinessException {

        //Validate the user who requested the change
        cashUserPersonaService.validateUserRole(userId, PersonaRole.BM);

        //Validate the role of the substitute
        cashUserPersonaService.validateUserRole(nikPic, new ArrayList<>(List.of(
                PersonaRole.LB.getPersonaRole(),
                PersonaRole.CO.getPersonaRole()
        )));

        RequisitionEntity requisitionEntity = findRequisitionById(requisitionId);
        requisitionEntity.setCreatedBy(nikPic);
        requisitionRepository.save(requisitionEntity);
    }

    private void reverseRequisition(
            Long requisitionId,
            String userId,
            boolean settlementReverse,
            String accessToken,
            String coCode, String rrn) throws BusinessException {

        RequisitionEntity requisitionEntity = findRequisitionById(requisitionId);
        if(!settlementReverse && (requisitionEntity.getRequestAmount().compareTo(requisitionEntity.getActualizedAmount()) != 0)) {

            CashUserPersonasEntity lbOfficer = cashUserPersonaService.findTop1PersonaByMmsAndRole(requisitionEntity.getMmsCode(), PersonaRole.LB.getPersonaRole());
            LBDailyTransactionProjection lbTransaction = fetchReturnTransaction(requisitionEntity);

            ResponseEntity response = postLbTransaction(
                    requisitionEntity,
                    lbOfficer,
                    lbTransaction,
                    userId,
                    accessToken,
                    coCode,
                    rrn);

            if(response.getStatusCode() == HttpStatus.OK) {
                //Push approval response notification
                notificationConfigurator.pushApprovalResponseNotification(
                        requisitionEntity.getCreatedBy(),
                        NotificationType.FPB_REVERSAL_APPROVED_BM
                );
            } else {
                if(response.getStatusCode().equals(HttpStatus.METHOD_FAILURE)) {
                    throw new BusinessException(HttpStatus.INTERNAL_SERVER_ERROR, "Denom tidak mencukupi");
                } else if (response.getStatusCode().equals(HttpStatus.PRECONDITION_FAILED)) {
                    throw new BusinessException(HttpStatus.INTERNAL_SERVER_ERROR, "Saldo tidak mencukupi");
                } else {
                    throw new BusinessException(response.getStatusCode(), ResponseMessage.ERROR_WHILE_POSTING_LB_TRANSACTION);
                }
            }

            notificationConfigurator.pushKfoDocNotification(
                    requisitionEntity.getMmsCode(),
                    NotificationType.FPB_REVERSAL_APPROVED_KFO
            );

        } else {

            if(!settlementReverse) {
                requisitionEntity.setReversed(true);
            }
            requisitionEntity.setReverseCounter(requisitionEntity.getReverseCounter()+1);
            logAndUpdateRequisitionStatus(
                    requisitionEntity,
                    RequisitionStatus.RV_APPROVED_BM,
                    userId,
                    REVERSAL);

            //Push approval response notification
            notificationConfigurator.pushApprovalResponseNotification(
                    requisitionEntity.getCreatedBy(),
                    NotificationType.FPB_REVERSAL_APPROVED_BM
            );
        }
    }



    public List<FPBReversalItemEntity> mapRequisitionItemsToFPBReversalItems(RequisitionEntity fpbRequestEntity,ReversalEntity fpbReversalEntity){
        return fpbRequestEntity.getRequisitionItems().stream().map(e-> FPBReversalItemEntity.builder()
                .fpbReversalId(fpbReversalEntity.getId())
                .paymentStatus(e.getPaymentStatus())
                .itemId(e.getItemId())
                .itemQty(e.getItemQty())
                .actualAmount(e.getActualAmount())
                .build()).collect(Collectors.toList());
    }

    private ResponseEntity postLbTransaction(RequisitionEntity requisitionEntity, CashUserPersonasEntity lbOfficer, LBDailyTransactionProjection lbTransaction, String userId, String accessToken, String coCode, String rrn) throws BusinessException {
        return externalCommService.postLbTransaction(
                LBDailyTransactionRequest.builder()
                        .openHour(Utility.getGMT7StringTimestampHourAndMinute())
                        .closeHour(Utility.getGMT7StringTimestampHourAndMinute())
                        .requestedBy(lbOfficer.getNik())
                        .mmsCode(requisitionEntity.getMmsCode())
                        .transactionTypeCO(lbTransaction.getTransactionType().equals(TransactionTypeLB.CASH_OUT) ? TransactionTypeCO.DEPOSIT : TransactionTypeCO.DEMAND)
                        .requestedType(RequestedType.FPB_REVERSAL)
                        .transactionType(lbTransaction.getTransactionType().equals(TransactionTypeLB.CASH_OUT) ? TransactionTypeLB.CASH_IN : TransactionTypeLB.CASH_OUT)
                        .totalAmount(lbTransaction.getTotalAmount())
                        .totalAmountCORequest(lbTransaction.getTotalAmount())
                        .hundreds(lbTransaction.getHundreds())
                        .seventyFives(lbTransaction.getSeventyFives())
                        .fiftys(lbTransaction.getFiftys())
                        .twentys(lbTransaction.getTwentys())
                        .tens(lbTransaction.getTens())
                        .fives(lbTransaction.getFives())
                        .smallMoney(lbTransaction.getSmallMoney())
                        .transactionStatus(String.valueOf(lbTransaction.getTransactionType().equals(TransactionTypeLB.CASH_OUT) ? RequisitionStatus.REIMBURSEMENT : RequisitionStatus.COLLECTED_LB))
                        .coDailyTransactionRequestSet(new HashSet<>(Collections.singletonList(
                                CoDailyTransactionRequest.builder()
                                        .requestAmount(lbTransaction.getTotalAmountRequest())
                                        .coName(lbOfficer.getCoName())
                                        .liquidity(BigDecimal.ZERO)
                                        .otherTransaction(BigDecimal.ZERO)
                                        .totalCollectedAmount(BigDecimal.ZERO)
                                        .totalEstimatedCollectedAmount(BigDecimal.ZERO)
                                        .totalEstimatedPRSAmount(BigDecimal.ZERO)
                                        .totalPrsAmount(BigDecimal.ZERO)
                                        .totalPrsPendingAmount(BigDecimal.ZERO)
                                        .coDailyTransactionRequestID(BigInteger.valueOf(requisitionEntity.getId()))
                                        .transactionTypeCO(lbTransaction.getTransactionType().equals(TransactionTypeLB.CASH_OUT) ? TransactionTypeCO.DEPOSIT : TransactionTypeCO.DEMAND)
                                        .transactionStatus(lbTransaction.getTransactionType().equals(TransactionTypeLB.CASH_OUT) ? CashManagementTransactionStatus.NEW : CashManagementTransactionStatus.COLLECTED_LB)
                                        .build()
                        )))
                        .user(new CashManagementApprovalUser(userId, null))
                        .journalSet(false)
                        .build(), userId, accessToken, coCode, rrn);
    }

    private LBDailyTransactionProjection fetchReturnTransaction(RequisitionEntity requisitionEntity) throws BusinessException {
        if(requisitionEntity.isReversed()) {
            return requisitionRepository.fetchReversedReturnTransactionByRequisitionId(requisitionEntity.getId()).orElseThrow(() -> new BusinessException(HttpStatus.BAD_REQUEST, ResponseMessage.BAD_REQUEST));
        } else {
            return requisitionRepository.fetchReturnTransactionByRequisitionId(requisitionEntity.getId()).orElseThrow(() -> new BusinessException(HttpStatus.BAD_REQUEST, ResponseMessage.BAD_REQUEST));
        }
    }

    private void validatePaymentStatus(RequisitionEntity requisitionEntity) throws BusinessException {
        if(requisitionEntity.getRequisitionItems()
                .stream()
                .allMatch(PAYMENT_STATUS_CANCELLED)) {
            throw new BusinessException(HttpStatus.BAD_REQUEST, ResponseMessage.STATUS_NOT_MATCH);
        } else {
            if(!requisitionEntity.getRequisitionItems()
                    .stream()
                    .filter(PAYMENT_STATUS_IS_NOT_CANCELLED)
                    .allMatch(PAYMENT_STATUS_ACTUALIZED)) {
                throw new BusinessException(HttpStatus.BAD_REQUEST, ResponseMessage.STATUS_NOT_MATCH);
            }
        }
    }

    public BigDecimal calculateItemTotalAmountByLifespan(RequisitionEntity fpbRequestEntity, RequestedType requestedType) {
        return fpbRequestEntity.getRequisitionItems()
                .stream()
                .filter(i -> requestedType.equals(RequestedType.FPB_ITEM_AOY) ?
                        i.getItemDetail().getLifespan().equalsIgnoreCase(ItemLifespan.ABOVE_1_YEAR.getItemLifespanValue()) :
                        i.getItemDetail().getLifespan().equalsIgnoreCase(ItemLifespan.BELOW_1_YEAR.getItemLifespanValue())
                                && i.getPaymentStatus() != PaymentStatus.CANCELLED)
                .map(RequisitionItemEntity::getActualAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

}
