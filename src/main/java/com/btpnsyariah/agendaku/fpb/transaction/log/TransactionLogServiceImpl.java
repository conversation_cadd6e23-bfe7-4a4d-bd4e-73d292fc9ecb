package com.btpnsyariah.agendaku.fpb.transaction.log;

import com.btpnsyariah.agendaku.fpb.exception.BusinessException;
import com.btpnsyariah.agendaku.fpb.model.CashUserPersonasEntity;
import com.btpnsyariah.agendaku.fpb.model.ResponseMessage;
import com.btpnsyariah.agendaku.fpb.transaction.requisition.RequisitionEntity;
import com.btpnsyariah.agendaku.fpb.transaction.requisition.RequisitionStatus;
import com.btpnsyariah.agendaku.fpb.user.CashUserPersonaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

@Component
public class TransactionLogServiceImpl implements TransactionLogService {
    
    @Autowired
    TransactionLogRepository transactionLogRepository;
    @Autowired
    CashUserPersonaService cashUserPersonaService;
    
    @Override
    public TransactionLogEntity logRequisition(RequisitionEntity requisitionEntity) throws BusinessException {
        CashUserPersonasEntity persona = cashUserPersonaService.findByNik(requisitionEntity.getCreatedBy());
        return transactionLogRepository.save(
                TransactionLogEntity.builder()
                        .requisitionId(requisitionEntity.getId())
                        .createdDate(new Timestamp(new Date().getTime()))
                        .createdBy(requisitionEntity.getCreatedBy())
                        .createdByName(persona.getCoName())
                        .transactionStatus(requisitionEntity.getRequestStatus())
                        .build()
        );
    }

    @Override
    public TransactionLogEntity logRequisition(Long requisitionId, RequisitionStatus requisitionStatus, String userId, String note) throws BusinessException {
        CashUserPersonasEntity persona = cashUserPersonaService.findByNik(userId);
        return transactionLogRepository.save(
                TransactionLogEntity.builder()
                        .requisitionId(requisitionId)
                        .note(note)
                        .createdDate(new Timestamp(new Date().getTime()))
                        .createdBy(userId)
                        .createdByName(persona.getCoName())
                        .transactionStatus(requisitionStatus)
                        .build()
        );
    }

    @Override
    public TransactionLogEntity logRequisition(Long requisitionId, RequisitionStatus requisitionStatus, String userId, String note, BigDecimal amount) throws BusinessException {
        CashUserPersonasEntity persona = cashUserPersonaService.findByNik(userId);
        return transactionLogRepository.save(
                TransactionLogEntity.builder()
                        .requisitionId(requisitionId)
                        .note(note)
                        .createdDate(new Timestamp(new Date().getTime()))
                        .createdBy(userId)
                        .createdByName(persona.getCoName())
                        .transactionStatus(requisitionStatus)
                        .amount(amount)
                        .build()
        );
    }

    @Override
    public boolean checkRequisitionTransactionLogStatus(Long requisitionId, String requisitionStatus) {
        return transactionLogRepository.checkRequisitionTransactionLogStatus(requisitionId, requisitionStatus);
    }


}
