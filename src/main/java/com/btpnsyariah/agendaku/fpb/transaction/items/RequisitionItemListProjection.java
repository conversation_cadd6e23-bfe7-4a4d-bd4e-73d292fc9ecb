package com.btpnsyariah.agendaku.fpb.transaction.items;

import com.btpnsyariah.agendaku.fpb.catalogue.ItemLifespan;

import java.math.BigDecimal;

public interface RequisitionItemListProjection {

    Long getId();
    Long getItemId();
    String getItemName();
    String getItemCategory();
    int getItemQty();
    String getMeasurementUnit();
    ItemLifespan getItemLifeSpan();
    BigDecimal getPrice();
    BigDecimal getActualAmount();
    PaymentStatus getPaymentStatus();

}
