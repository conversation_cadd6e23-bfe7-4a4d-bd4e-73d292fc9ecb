package com.btpnsyariah.agendaku.fpb.transaction.invoice;

import com.btpnsyariah.agendaku.fpb.exception.BusinessException;
import com.btpnsyariah.agendaku.fpb.model.BaseResponse;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Set;

public interface InvoiceService {

    @Transactional(propagation = Propagation.REQUIRES_NEW, isolation = Isolation.READ_UNCOMMITTED, rollbackFor = {Exception.class})
    ResponseEntity<BaseResponse> uploadInvoice(
            Long requisitionId,
            Long itemId,
            String token,
            MultipartFile invoice) throws IOException, BusinessException;

    ResponseEntity getInvoice(String token, String invoiceId);

    @Transactional(propagation = Propagation.REQUIRES_NEW, isolation = Isolation.READ_UNCOMMITTED, rollbackFor = {Exception.class})
    ResponseEntity deleteInvoice(String token, String invoiceId) throws BusinessException;

    Set<String> findInvoiceByRequisitionItemId(Long itemId);

    void reverseItemInvoices(Long requisitionId);
}
