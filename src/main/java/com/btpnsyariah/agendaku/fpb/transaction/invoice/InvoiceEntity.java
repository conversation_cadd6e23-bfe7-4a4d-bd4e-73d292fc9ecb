package com.btpnsyariah.agendaku.fpb.transaction.invoice;

import com.btpnsyariah.agendaku.fpb.transaction.items.RequisitionItemEntity;
import com.fasterxml.jackson.annotation.JsonBackReference;
import lombok.*;

import javax.persistence.*;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "FPBInvoice", schema = "dbo")
public class InvoiceEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "Id", updatable = false)
    private Long id;

    @Column(name = "RequisitionItemId")
    private Long requisitionItemId;

    @Column(name = "InvoiceId")
    private String invoiceId;

    @Column(name = "IsDeleted")
    private boolean isDeleted;

    @ManyToOne
    @JoinColumn(name = "RequisitionItemId", insertable = false, updatable = false)
    private RequisitionItemEntity requisitionItemEntity;

    public InvoiceEntity(Long requisitionItemId, String invoiceId, boolean isDeleted) {
        this.requisitionItemId = requisitionItemId;
        this.invoiceId = invoiceId;
        this.isDeleted = isDeleted;
    }
}
