package com.btpnsyariah.agendaku.fpb.transaction.requisition;

import com.btpnsyariah.agendaku.fpb.exception.BusinessException;
import com.btpnsyariah.agendaku.fpb.model.BaseResponse;
import com.btpnsyariah.agendaku.fpb.model.ResponseMessage;
import com.btpnsyariah.agendaku.fpb.util.Utility;
import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/requisitions")
@Api(tags = "Requisitions")
public class RequisitionController {

    @Autowired
    private RequisitionService requisitionService;

    private static final Logger LOGGER = LoggerFactory.getLogger(RequisitionController.class);

    private static final String LOG_ERROR = "[RequisitionController] [{}] There was an error in {} : {} | {}";

    @Operation(summary = "Get requisition list")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Requisition list fetched",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) }),
            @ApiResponse(responseCode = "500", description = "Failed to fetch requisition list",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) })
    })
    @GetMapping
    public ResponseEntity<BaseResponse> getRequisitionList(
            @RequestParam("approval") boolean approval,
            @RequestParam(required = false, value = "hist") boolean history,
            @RequestHeader("UserId") String nik,
            @RequestParam(required = false, value = "nik") String nikParam,
            @RequestParam(required = false, value = "mmsCode") String mmsCode) {
        try {
            return ResponseEntity.ok(new BaseResponse(
                    true,
                    ResponseMessage.REQUISITION_LIST_FETCHED,
                    requisitionService.getRequisitions(nik,nikParam, approval, history, mmsCode)
            ));
        } catch (BusinessException e){
            String traceCode = Utility.getTraceCode();
            LOGGER.error(LOG_ERROR,
                    traceCode,
                    Utility.getStackTraceMethodClean(e),
                    e.getMessage(),
                    Utility.getStackTrace(e));
            return new ResponseEntity<>(new BaseResponse(
                    false,
                    e.getMessage(),
                    traceCode
            ), e.getHttpStatus());
        } catch (Exception e) {
            String traceCode = Utility.getTraceCode();
            LOGGER.error(LOG_ERROR,
                    traceCode,
                    Utility.getStackTraceMethodClean(e),
                    e.getMessage(),
                    Utility.getStackTrace(e));
            return new ResponseEntity<>(new BaseResponse(
                    false,
                    ResponseMessage.SOMETHING_WENT_WRONG,
                    traceCode
            ), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Submit requisition")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Requisition submitted",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) }),
            @ApiResponse(responseCode = "500", description = "Failed to submit requisition",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) })
    })
    @PostMapping(consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<BaseResponse> submitRequisition(
            @RequestHeader("UserId") String userId,
            @RequestHeader("mmsCode") String mmsCode,
            @RequestBody RequisitionEntity requisitionEntity) {
        try {
            return ResponseEntity.ok(new BaseResponse(
                    true,
                    requisitionService.submitRequisition(userId, mmsCode, requisitionEntity)
            ));
        } catch (BusinessException e){
            String traceCode = Utility.getTraceCode();
            LOGGER.error(LOG_ERROR,
                    traceCode,
                    Utility.getStackTraceMethodClean(e),
                    e.getMessage(),
                    Utility.getStackTrace(e));
            return new ResponseEntity<>(new BaseResponse(
                    false,
                    e.getMessage(),
                    traceCode
            ), HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            String traceCode = Utility.getTraceCode();
            LOGGER.error(LOG_ERROR,
                    traceCode,
                    Utility.getStackTraceMethodClean(e),
                    e.getMessage(),
                    Utility.getStackTrace(e));
            return new ResponseEntity<>(new BaseResponse(
                    false,
                    ResponseMessage.SOMETHING_WENT_WRONG,
                    traceCode
            ), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Submit Approval requisition")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Approval Requisition submitted",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) }),
            @ApiResponse(responseCode = "500", description = "Failed to submit approval  requisition",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) })
    })
    @PostMapping(consumes = MediaType.APPLICATION_JSON_VALUE, path = "/approval")
    public ResponseEntity<BaseResponse> submitApprovalRequisition(
            @RequestHeader("UserId") String userId,
            @RequestHeader("Access-Token") String accessToken,
            @RequestHeader("CoCode") String coCode,
            @RequestHeader("RetrievalReferenceNumber") String rrn,
            @RequestHeader("deviceId") String deviceId,
            @RequestBody RequisitionApprovalDTO requisitionApprovalDTO) {
        try {
            requisitionService.submitApprovalRequisitions(
                    requisitionApprovalDTO,
                    userId,
                    accessToken,
                    coCode,
                    rrn,
                    deviceId);
            return ResponseEntity.ok(new BaseResponse(
                    true,
                    ResponseMessage.APPROVAL_SUBMITTED
            ));
        } catch (BusinessException e) {
            String traceCode = Utility.getTraceCode();
            LOGGER.error(LOG_ERROR,
                    traceCode,
                    Utility.getStackTraceMethodClean(e),
                    e.getMessage(),
                    Utility.getStackTrace(e));
            return new ResponseEntity<>(new BaseResponse(
                    false,
                    String.format(ResponseMessage.SOMETHING_WENT_WRONG_ARG, e.getMessage()),
                    traceCode
            ), e.getHttpStatus());
        } catch (Exception e) {
            String traceCode = Utility.getTraceCode();
            LOGGER.error(LOG_ERROR,
                    traceCode,
                    Utility.getStackTraceMethodClean(e),
                    e.getMessage(),
                    Utility.getStackTrace(e));
            return new ResponseEntity<>(new BaseResponse(
                    false,
                    ResponseMessage.SOMETHING_WENT_WRONG,
                    traceCode
            ), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Submit requisition settlement")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Requisition settled",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) }),
            @ApiResponse(responseCode = "500", description = "Failed to settle requisition",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) })
    })
    @PostMapping(path = "/settlement/{requisitionId}")
    public ResponseEntity<BaseResponse> submitRequisitionSettlement(
            @PathVariable("requisitionId") Long requisitionId,
            @RequestHeader("UserId") String userId,
            @RequestBody long[] cancelledItemIds) {
        try {
            requisitionService.submitRequisitionSettlement(requisitionId,userId, cancelledItemIds);
            return ResponseEntity.ok(new BaseResponse(
                    true,
                    ResponseMessage.REQUISITION_SETTLED
            ));
        } catch (BusinessException e) {
            String traceCode = Utility.getTraceCode();
            LOGGER.error(LOG_ERROR,
                    traceCode,
                    Utility.getStackTraceMethodClean(e),
                    e.getMessage(),
                    Utility.getStackTrace(e));
            return new ResponseEntity<>(new BaseResponse(
                    false,
                    String.format(ResponseMessage.SOMETHING_WENT_WRONG_ARG, e.getMessage()),
                    traceCode
            ), e.getHttpStatus());
        } catch (Exception e) {
            String traceCode = Utility.getTraceCode();
            LOGGER.error(LOG_ERROR,
                    traceCode,
                    Utility.getStackTraceMethodClean(e),
                    e.getMessage(),
                    Utility.getStackTrace(e));
            return new ResponseEntity<>(new BaseResponse(
                    false,
                    ResponseMessage.SOMETHING_WENT_WRONG,
                    traceCode
            ), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Cancel requisition")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Requisition cancelled",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) }),
            @ApiResponse(responseCode = "500", description = "Failed to cancel requisition",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) })
    })
    @PatchMapping(path = "/cancel/{requisitionId}")
    public ResponseEntity<BaseResponse> requestCancelRequisition(
            @PathVariable("requisitionId") Long requisitionId,
            @RequestHeader("UserId") String userId,
            @RequestBody(required = false) String note) {
        try {
            requisitionService.requestCancelRequisition(requisitionId,userId,note);
            return ResponseEntity.ok(new BaseResponse(
                    true,
                    ResponseMessage.REQUISITION_CANCELLED
            ));
        } catch (BusinessException e) {
            String traceCode = Utility.getTraceCode();
            LOGGER.error(LOG_ERROR,
                    traceCode,
                    Utility.getStackTraceMethodClean(e),
                    e.getMessage(),
                    Utility.getStackTrace(e));
            return new ResponseEntity<>(new BaseResponse(
                    false,
                    String.format(ResponseMessage.SOMETHING_WENT_WRONG_ARG, e.getMessage()),
                    traceCode
            ), e.getHttpStatus());
        } catch (Exception e) {
            String traceCode = Utility.getTraceCode();
            LOGGER.error(LOG_ERROR,
                    traceCode,
                    Utility.getStackTraceMethodClean(e),
                    e.getMessage(),
                    Utility.getStackTrace(e));
            return new ResponseEntity<>(new BaseResponse(
                    false,
                    ResponseMessage.SOMETHING_WENT_WRONG,
                    traceCode
            ), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Reverse requisition")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Requisition reverse requested",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) }),
            @ApiResponse(responseCode = "500", description = "Failed to request reverse requisition",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) })
    })
    @PatchMapping(path = "/reverse/{requisitionId}")
    public ResponseEntity<BaseResponse> requestReverseRequisition(
            @PathVariable("requisitionId") Long requisitionId,
            @RequestParam("settlement") boolean settlement,
            @RequestParam(required = false, value = "change-pic") Boolean changePic,
            @RequestParam(required = false, value = "pic") String nikPic,
            @RequestHeader("UserId") String userId,
            @RequestBody(required = false) String note) {
        try {
            if(changePic != null && changePic) {
                requisitionService.changePic(
                        requisitionId,
                        nikPic,
                        userId);
            } else {
                requisitionService.requestReverseRequisition(requisitionId,settlement,userId,note);
            }
            return ResponseEntity.ok(new BaseResponse(
                    true,
                    ResponseMessage.REQUISITION_REVERSED
            ));
        } catch (BusinessException e) {
            String traceCode = Utility.getTraceCode();
            LOGGER.error(LOG_ERROR,
                    traceCode,
                    Utility.getStackTraceMethodClean(e),
                    e.getMessage(),
                    Utility.getStackTrace(e));
            return new ResponseEntity<>(new BaseResponse(
                    false,
                    String.format(ResponseMessage.SOMETHING_WENT_WRONG_ARG, e.getMessage()),
                    traceCode
            ), e.getHttpStatus());
        } catch (Exception e) {
            String traceCode = Utility.getTraceCode();
            LOGGER.error(LOG_ERROR,
                    traceCode,
                    Utility.getStackTraceMethodClean(e),
                    e.getMessage(),
                    Utility.getStackTrace(e));
            return new ResponseEntity<>(new BaseResponse(
                    false,
                    ResponseMessage.SOMETHING_WENT_WRONG,
                    traceCode
            ), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Upload berita acara", parameters = {
            @Parameter(in = ParameterIn.PATH
                    , description = "RequisitionId"
                    , name = "requisitionId"
                    , content = @Content(schema = @Schema(type = "Long", defaultValue = "null"))),
            @Parameter(in = ParameterIn.HEADER
                    , description = "User Access Token"
                    , name = "Access-Token"
                    , content = @Content(schema = @Schema(type = "String", defaultValue = "null")))
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Berita acara uploaded",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) }),
            @ApiResponse(responseCode = "4xx", description = "Failed to upload berita acara",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) }),
            @ApiResponse(responseCode = "500", description = "Failed to upload berita acara",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) })
    })
    @PostMapping(value = "/upload/{requisitionId}", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    public ResponseEntity<BaseResponse> uploadBeritaAcara(
            @PathVariable("requisitionId") Long requisitionId,
            @RequestPart MultipartFile beritaAcara,
            @RequestHeader("Access-Token") String token) {
        try {
            return requisitionService.uploadBeritaAcara(
                    requisitionId,
                    token,
                    beritaAcara);
        } catch (BusinessException e) {
            String traceCode = Utility.getTraceCode();
            LOGGER.error(LOG_ERROR,
                    traceCode,
                    Utility.getStackTraceMethodClean(e),
                    e.getMessage(),
                    Utility.getStackTrace(e));
            return new ResponseEntity<>(new BaseResponse(
                    false,
                    String.format(ResponseMessage.SOMETHING_WENT_WRONG_ARG, e.getMessage()),
                    traceCode
            ), e.getHttpStatus());
        } catch(HttpClientErrorException e) {
            String traceCode = Utility.getTraceCode();
            LOGGER.error(LOG_ERROR,
                    traceCode,
                    Utility.getStackTraceMethodClean(e),
                    e.getMessage(),
                    Utility.getStackTrace(e));
            return new ResponseEntity<>(new BaseResponse(
                    false,
                    String.format(ResponseMessage.SOMETHING_WENT_WRONG_ARG, e.getMessage()),
                    traceCode
            ), e.getStatusCode());
        } catch (Exception e) {
            String traceCode = Utility.getTraceCode();
            LOGGER.error(LOG_ERROR,
                    traceCode,
                    Utility.getStackTraceMethodClean(e),
                    e.getMessage(),
                    Utility.getStackTrace(e));
            return new ResponseEntity<>(new BaseResponse(
                    false,
                    ResponseMessage.SOMETHING_WENT_WRONG,
                    traceCode
            ), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Get berita acara status", parameters = {
            @Parameter(in = ParameterIn.PATH
                    , description = "RequisitionId"
                    , name = "requisitionId"
                    , content = @Content(schema = @Schema(type = "Long", defaultValue = "null")))
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Berita acara status fetched",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) }),
            @ApiResponse(responseCode = "4xx", description = "Failed to get berita acara status",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) }),
            @ApiResponse(responseCode = "500", description = "Failed to get berita acara status",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) })
    })
    @GetMapping(value = "/documents/status/{requisitionId}")
    public ResponseEntity getBeritaAcaraStatus(@PathVariable("requisitionId") Long requisitionId) {
        try {
            return ResponseEntity.ok(new BaseResponse(
                    true,
                    ResponseMessage.REQUISITION_BA_STATUS_FETCHED,
                    requisitionService.getBeritaAcaraStatus(requisitionId)
            ));
        } catch (Exception e) {
            String traceCode = Utility.getTraceCode();
            LOGGER.error(LOG_ERROR,
                    traceCode,
                    Utility.getStackTraceMethodClean(e),
                    e.getMessage(),
                    Utility.getStackTrace(e));
            return new ResponseEntity<>(new BaseResponse(
                    false,
                    ResponseMessage.SOMETHING_WENT_WRONG,
                    traceCode
            ), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Get berita acara", parameters = {
            @Parameter(in = ParameterIn.PATH
                    , description = "RequisitionId"
                    , name = "requisitionId"
                    , content = @Content(schema = @Schema(type = "Long", defaultValue = "null"))),
            @Parameter(in = ParameterIn.HEADER
                    , description = "User Access Token"
                    , name = "Access-Token"
                    , content = @Content(schema = @Schema(type = "String", defaultValue = "null")))
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Berita acara fetched",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) }),
            @ApiResponse(responseCode = "4xx", description = "Failed to get berita acara",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) }),
            @ApiResponse(responseCode = "500", description = "Failed to get berita acara",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) })
    })
    @GetMapping(value = "/documents/{requisitionId}")
    public ResponseEntity getBeritaAcara(
            @PathVariable("requisitionId") Long requisitionId,
            @RequestHeader("Access-Token") String token) {
        try {
            return requisitionService.getBeritaAcara(
                    requisitionId,
                    token);
        } catch(HttpClientErrorException e) {
            String traceCode = Utility.getTraceCode();
            LOGGER.error(LOG_ERROR,
                    traceCode,
                    Utility.getStackTraceMethodClean(e),
                    e.getMessage(),
                    Utility.getStackTrace(e));
            return new ResponseEntity<>(new BaseResponse(
                    false,
                    String.format(ResponseMessage.SOMETHING_WENT_WRONG_ARG, e.getMessage()),
                    traceCode
            ), e.getStatusCode());
        } catch (Exception e) {
            String traceCode = Utility.getTraceCode();
            LOGGER.error(LOG_ERROR,
                    traceCode,
                    Utility.getStackTraceMethodClean(e),
                    e.getMessage(),
                    Utility.getStackTrace(e));
            return new ResponseEntity<>(new BaseResponse(
                    false,
                    ResponseMessage.SOMETHING_WENT_WRONG,
                    traceCode
            ), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Upload berita acara", parameters = {
            @Parameter(in = ParameterIn.PATH
                    , description = "RequisitionId"
                    , name = "requisitionId"
                    , content = @Content(schema = @Schema(type = "Long", defaultValue = "null"))),
            @Parameter(in = ParameterIn.HEADER
                    , description = "User Access Token"
                    , name = "Access-Token"
                    , content = @Content(schema = @Schema(type = "String", defaultValue = "null")))
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Requisition PIC changed",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) }),
            @ApiResponse(responseCode = "4xx", description = "Failed to change requisition PIC",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) }),
            @ApiResponse(responseCode = "500", description = "Failed to change requisition PIC",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) })
    })
    @PatchMapping(value = "/change-pic/{requisitionId}")
    public ResponseEntity<BaseResponse> changeRequisitionPic(
            @PathVariable("requisitionId") Long requisitionId,
            @RequestParam("pic") String nikPic,
            @RequestHeader("UserId") String userId) {
        try {
            requisitionService.changePic(
                    requisitionId,
                    nikPic,
                    userId);
            return ResponseEntity.ok(new BaseResponse(
                    true,
                    ResponseMessage.REQUISITION_PIC_CHANGED
            ));
        } catch (BusinessException e) {
            String traceCode = Utility.getTraceCode();
            LOGGER.error(LOG_ERROR,
                    traceCode,
                    Utility.getStackTraceMethodClean(e),
                    e.getMessage(),
                    Utility.getStackTrace(e));
            return new ResponseEntity<>(new BaseResponse(
                    false,
                    String.format(ResponseMessage.SOMETHING_WENT_WRONG_ARG, e.getMessage()),
                    traceCode
            ), e.getHttpStatus());
        } catch (Exception e) {
            String traceCode = Utility.getTraceCode();
            LOGGER.error(LOG_ERROR,
                    traceCode,
                    Utility.getStackTraceMethodClean(e),
                    e.getMessage(),
                    Utility.getStackTrace(e));
            return new ResponseEntity<>(new BaseResponse(
                    false,
                    ResponseMessage.SOMETHING_WENT_WRONG,
                    traceCode
            ), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
