package com.btpnsyariah.agendaku.fpb.transaction.xlink;

import com.btpnsyariah.agendaku.fpb.model.RequestedType;
import com.btpnsyariah.agendaku.fpb.transaction.requisition.RequisitionEntity;
import com.btpnsyariah.agendaku.fpb.transaction.requisition.RequisitionStatus;
import com.btpnsyariah.agendaku.fpb.util.Utility;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

@Service
public class XlinkService {

    @Autowired
    XlinkLogRepository xlinkLogRepository;
    public void saveXlinklog(RequisitionEntity requisitionEntity, RequestedType requestedType, BigDecimal amount, String userId, String deviceId){
        Timestamp now = Utility.getTimestamptUTCNow();
        xlinkLogRepository.save(XlinkLogEntity.builder()
                .createdDate(now)
                .createdBy(userId)
                .updatedDate(now)
                .updatedBy(userId)
                .transactionId(requisitionEntity.getId())
                .mmsCode(requisitionEntity.getMmsCode())
                .requestedType(requestedType)
                .transactionStatus(RequisitionStatus.IN_PROGRESS.toString())
                .responseCode(0)
                .deviceId(deviceId)
                .amount(amount)
                .build()
        );
    }
}
