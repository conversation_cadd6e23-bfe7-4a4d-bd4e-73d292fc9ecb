package com.btpnsyariah.agendaku.fpb.transaction.invoice;

import com.btpnsyariah.agendaku.fpb.exception.BusinessException;
import com.btpnsyariah.agendaku.fpb.model.BaseResponse;
import com.btpnsyariah.agendaku.fpb.model.ResponseMessage;
import com.btpnsyariah.agendaku.fpb.service.StorageService;
import com.btpnsyariah.agendaku.fpb.transaction.items.ItemService;
import com.btpnsyariah.agendaku.fpb.transaction.items.RequisitionItemEntity;
import com.btpnsyariah.agendaku.fpb.transaction.requisition.RequisitionEntity;
import com.btpnsyariah.agendaku.fpb.transaction.requisition.RequisitionService;
import com.btpnsyariah.agendaku.fpb.transaction.requisition.RequisitionStatus;
import com.btpnsyariah.agendaku.fpb.util.Utility;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Set;

@Component
public class InvoiceServiceImpl implements InvoiceService {

    public static final String UPLOAD_INVOICE = "Upload bukti pembayaran";
    @Autowired
    StorageService storageService;
    @Autowired
    RequisitionService requisitionService;
    @Autowired
    ItemService itemService;
    @Autowired
    InvoiceRepository invoiceRepository;

    @Override
    public ResponseEntity<BaseResponse> uploadInvoice(
            Long requisitionId,
            Long itemId,
            String token,
            MultipartFile invoice) throws IOException, BusinessException {

        RequisitionEntity requisitionEntity = requisitionService.findRequisitionById(requisitionId);
        itemService.itemExist(requisitionEntity, itemId);

        String invoiceId = Utility.getTraceCode() +
                "-" +
                requisitionEntity.getId().toString() +
                itemId.toString();

        ResponseEntity response = storageService.send(
                invoice,
                token,
                invoiceId,
                HttpMethod.PUT,
                MediaType.IMAGE_JPEG);

        if(response.getStatusCode() == HttpStatus.OK || response.getStatusCode() == HttpStatus.NO_CONTENT) {

            savePayment(requisitionId, itemId, invoiceId);

            if(requisitionEntity.getRequestStatus() != RequisitionStatus.IN_PROGRESS) {
                //Change requisition status to IN_PROGRESS
                requisitionService.logAndUpdateRequisitionStatus(
                        requisitionEntity,
                        RequisitionStatus.IN_PROGRESS,
                        requisitionEntity.getCreatedBy(),
                        UPLOAD_INVOICE);
            }

            return ResponseEntity.ok(new BaseResponse(true, ResponseMessage.INVOICE_UPLOADED));
        } else {
            String responseBody = response.getBody() != null ? response.getBody().toString() : "N/A";
            return new ResponseEntity<>(new BaseResponse(false, String.format(ResponseMessage.INVOICE_UPLOAD_FAIL, responseBody)), response.getStatusCode());
        }
    }

    private void savePayment(Long requisitionId, Long itemId, String invoiceId) throws BusinessException {
        RequisitionItemEntity requisitionItemEntity = itemService.findItemByRequisitionIdAndItemId(requisitionId, itemId);
        invoiceRepository.save(new InvoiceEntity(
                requisitionItemEntity.getId(),
                invoiceId,
                false
        ));
        itemService.checkAndUpdatePaymentStatusByInvoiceId(invoiceId);
    }

    @Override
    public ResponseEntity getInvoice(String token, String invoiceId) {
        ResponseEntity response = storageService.send(
                token,
                invoiceId,
                HttpMethod.GET);

        if(response.getStatusCode() == HttpStatus.OK || response.getStatusCode() == HttpStatus.NO_CONTENT) {
            return response;
        } else {
            String responseBody = response.getBody() != null ? response.getBody().toString() : "N/A";
            return new ResponseEntity<>(new BaseResponse(false, String.format(ResponseMessage.INVOICE_FETCH_FAIL, responseBody)), response.getStatusCode());
        }
    }

    @Override
    public ResponseEntity deleteInvoice(String token, String invoiceId) throws BusinessException {

        ResponseEntity response = storageService.send(
                token,
                invoiceId,
                HttpMethod.DELETE);

        if(response.getStatusCode() == HttpStatus.OK || response.getStatusCode() == HttpStatus.NO_CONTENT) {
            invoiceRepository.deleteInvoice(invoiceId);
            itemService.checkAndUpdatePaymentStatusByInvoiceId(invoiceId);
            return new ResponseEntity<>(new BaseResponse(true, ResponseMessage.INVOICE_DELETED), HttpStatus.OK);
        } else {
            String responseBody = response.getBody() != null ? response.getBody().toString() : "N/A";
            return new ResponseEntity<>(new BaseResponse(false, String.format(ResponseMessage.INVOICE_DELETE_FAIL, responseBody)), response.getStatusCode());
        }
    }

    @Override
    public Set<String> findInvoiceByRequisitionItemId(Long itemId) {
        return invoiceRepository.findInvoiceByRequisitionItemId(itemId);
    }

    @Override
    public void reverseItemInvoices(Long requisitionId) {
        invoiceRepository.reverseItemInvoices(requisitionId);
    }
}
