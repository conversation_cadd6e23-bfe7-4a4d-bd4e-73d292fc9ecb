package com.btpnsyariah.agendaku.fpb.transaction.approval;

import com.btpnsyariah.agendaku.fpb.transaction.log.TransactionLogService;
import com.btpnsyariah.agendaku.fpb.transaction.requisition.RequisitionRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ApprovalServiceImpl {

    @Autowired
    RequisitionRepository requisitionRepository;

    @Autowired
    TransactionLogService transactionLogService;


    public void submitApproval(){

    }
}
