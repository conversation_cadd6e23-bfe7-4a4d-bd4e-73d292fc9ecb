package com.btpnsyariah.agendaku.fpb.transaction.requisition;

import com.btpnsyariah.agendaku.fpb.transaction.items.PaymentStatus;
import lombok.*;

import javax.persistence.*;
import java.math.BigDecimal;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Builder
@Table(name = "FPBReversalItems", schema = "dbo")
public class FPBReversalItemEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "Id", nullable = false, updatable = false)
    private Long id;

    @Column(name = "ItemId")
    private Long itemId;

    @Column(name = "FPBReversalId")
    private Long fpbReversalId;

    @Column(name = "ItemQty")
    private int itemQty;

    @Column(name = "ActualAmount")
    private BigDecimal actualAmount;

    @Enumerated(EnumType.STRING)
    @Column(name = "PaymentStatus")
    private PaymentStatus paymentStatus;


}
