package com.btpnsyariah.agendaku.fpb.transaction.requisition;

import com.btpnsyariah.agendaku.fpb.transaction.items.RequisitionItemEntity;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import java.util.Set;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "FPBRequest", schema = "dbo")
public class RequisitionEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "Id", nullable = false, updatable = false)
    private Long id;

    @Column(name = "RequisitionNo")
    private String requisitionNo;

    @Column(name = "MMSCode")
    private String mmsCode;

    @Column(name = "CreatedBy")
    private String createdBy;

    @Column(name = "CreatedDate")
    private Timestamp createdDate;

    @Column(name = "RequestAmount")
    private BigDecimal requestAmount;

    @Column(name = "RawRequestAmount")
    private BigDecimal rawRequestAmount;

    @Enumerated(EnumType.STRING)
    @Column(name = "RequestStatus")
    private RequisitionStatus requestStatus;

    @Column(name = "Notes")
    private String notes;

    @Column(name = "Telephone")
    private String telephone;

    @Column(name = "Address")
    private String address;

    @Column(name = "RecipientName")
    private String recipientName;

    @Column(name = "ActualizedAmount")
    private BigDecimal actualizedAmount;

    @Column(name = "LbDailyTransactionId")
    private Long lbDailyId;

    @Column(name = "AdditionalSettlementId")
    private Long additionalSettlementId;

    @Column(name = "IsReversed")
    private boolean isReversed;

    @Column(name = "RefundId")
    private Long refundId;

    @Column(name = "DisbursementAutopost")
    private boolean disbursementAutopost;

    @Column(name = "ActualizationAutopost")
    private boolean actualizationAutopost;

    @Column(name = "SettlementAutopost")
    private boolean settlementAutopost;

    @Column(name = "ReverseCounter")
    private int reverseCounter;

    @OneToMany(mappedBy = "requisitionEntity", cascade = CascadeType.ALL)
    private Set<RequisitionItemEntity> requisitionItems;

    @PrePersist
    public void prePersistNulls() {
        if (requestAmount == null) {
            requestAmount = BigDecimal.ZERO;
        }

        if (createdDate == null) {
            createdDate = new Timestamp(new Date().getTime());
        }

        if (requestStatus == null) {
            requestStatus = RequisitionStatus.SUBMITTED;
        }

        if (actualizedAmount == null) {
            actualizedAmount = BigDecimal.ZERO;
        }
    }

    public RequisitionEntity(String mmsCode, String createdBy, Timestamp createdDate, RequisitionStatus requestStatus) {
        this.mmsCode = mmsCode;
        this.createdBy = createdBy;
        this.createdDate = createdDate;
        this.requestStatus = requestStatus;
    }

    public RequisitionEntity(Long id, String requisitionNo, String mmsCode, String createdBy, Timestamp createdDate, BigDecimal requestAmount, RequisitionStatus requestStatus, String notes, String telephone, String address, String recipientName, Set<RequisitionItemEntity> requisitionItems) {
        this.id = id;
        this.requisitionNo = requisitionNo;
        this.mmsCode = mmsCode;
        this.createdBy = createdBy;
        this.createdDate = createdDate;
        this.requestAmount = requestAmount;
        this.requestStatus = requestStatus;
        this.notes = notes;
        this.telephone = telephone;
        this.address = address;
        this.recipientName = recipientName;
        this.requisitionItems = requisitionItems;
    }
}
