package com.btpnsyariah.agendaku.fpb.document;

public enum DocumentType {
        BPKT("BPKT"),
        BPKT_LIKUIDITAS("BPKT"),
        BPKT_LIKUIDITAS_PDF("BPKT_PDF"),
        REVERSE_BPKT("Reverse BPKT"),
        REVERSE_BPKT_LIKUIDITAS("Reverse BPKT"),
        REVERSE_BPKT_LIKUIDITAS_PDF("Reverse BPKT"),
        FPB("FPB"),
        FPB_BA("Berita Acara FPB"),


        REVERSE_FPB("REVERSE_FPB"),

        FPCD("FPCD"),
        BRW("BRW"),
        REVERSE_BRW("REVERSE_BRW"),
        REVERSE_BRW_SETTLEMENT("REVERSE_BRW_SETTLEMENT"),
        CASH_OPNAME("CASH_OPNAME"),
        CASH_COUNT("CASH_COUNT"),
        ASURANSI_OVERLIMIT("ASURANSI_OVERLIMIT"),
        ASURANSI("ASURANSI"),
        VCO("VIRTUAL_CASH_OPNAME"),
        VCO_LOA("VIRTUAL_CASH_OPNAME_LETTER_OF_ASSIGNMENT"),
        STOCK_OPNAME("STOCK_OPNAME");


        private final String documentType;

        DocumentType(String documenType) { this.documentType = documenType; }

        public String documenType() { return documentType; }
}
