package com.btpnsyariah.agendaku.fpb.document;

import com.btpnsyariah.agendaku.fpb.model.RequestedType;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigInteger;
import java.sql.Timestamp;

@Entity
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "DocumentReport", schema = "dbo")
public class DocumentEntity implements Serializable {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id")
  private Long id;

  @Column(name = "MMSCode")
  private String mmsCode;

  @Column(name = "TransactionId")
  private BigInteger transactionId;

  @Enumerated(EnumType.STRING)
  @Column(name = "RequestedType")
  private RequestedType requestedType;

  @Enumerated(EnumType.STRING)
  @Column(name = "DocumentType")
  private DocumentType documentType;

  @Column(name = "TransactionStatus")
  private String transactionStatus;

  @Column(name = "CreatedDate")
  private Timestamp createdDate;

  @Column(name = "CreatedBy")
  private String createdBy;

  @Column(name = "IsCancelled")
  private Boolean isCancelled;

  public Boolean getIsCancelled() {
    if (this.isCancelled == null){
    return false;
    } else {
      return this.isCancelled;
    }
  }

  public void setIsCancelled(Boolean isCancelled) {
    this.isCancelled = isCancelled;
  }

  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public String getMmsCode() {
    return mmsCode;
  }

  public void setMmsCode(String mmsCode) {
    this.mmsCode = mmsCode;
  }

  public BigInteger getTransactionId() {
    return transactionId;
  }

  public void setTransactionId(BigInteger transactionId) {
    this.transactionId = transactionId;
  }

  public RequestedType getRequestedType() {
    return requestedType;
  }

  public void setRequestedType(RequestedType requestedType) {
    this.requestedType = requestedType;
  }

  public String getTransactionStatus() {
    return transactionStatus;
  }

  public void setTransactionStatus(String transactionStatus) {
    this.transactionStatus = transactionStatus;
  }

  public Timestamp getCreatedDate() {
    return createdDate;
  }

  public void setCreatedDate(Timestamp createdDate) {
    this.createdDate = createdDate;
  }

  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public DocumentType getDocumentType() {
    return documentType;
  }

  public void setDocumentType(DocumentType documentType) {
    this.documentType = documentType;
  }

  public DocumentEntity(String mmsCode, BigInteger transactionId, DocumentType documentType, String transactionStatus, Timestamp createdDate, String createdBy) {
    this.mmsCode = mmsCode;
    this.transactionId = transactionId;
    this.documentType = documentType;
    this.transactionStatus = transactionStatus;
    this.createdDate = createdDate;
    this.createdBy = createdBy;
  }
}
