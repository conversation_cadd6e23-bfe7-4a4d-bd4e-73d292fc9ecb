package com.btpnsyariah.agendaku.fpb.document;

import com.btpnsyariah.agendaku.fpb.transaction.requisition.RequisitionEntity;
import com.btpnsyariah.agendaku.fpb.transaction.requisition.RequisitionStatus;
import com.btpnsyariah.agendaku.fpb.transaction.requisition.ReversalEntity;

public interface DocumentService {

    void saveFPBDocument(RequisitionStatus requisitionStatus, RequisitionEntity requisitionEntity, DocumentType documentType);
    void saveReverseFPBDocument(RequisitionEntity requisitionEntity, Long reversalId, DocumentType documentType,String createdBy);
}
