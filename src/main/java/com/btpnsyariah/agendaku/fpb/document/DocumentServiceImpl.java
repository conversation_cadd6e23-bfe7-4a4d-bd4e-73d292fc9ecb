package com.btpnsyariah.agendaku.fpb.document;

import com.btpnsyariah.agendaku.fpb.transaction.requisition.RequisitionEntity;
import com.btpnsyariah.agendaku.fpb.transaction.requisition.RequisitionStatus;
import com.btpnsyariah.agendaku.fpb.transaction.requisition.ReversalEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigInteger;
import java.sql.Timestamp;
import java.util.Date;

@Component
public class DocumentServiceImpl implements DocumentService {

    @Autowired
    DocumentRepository documentRepository;

    @Override
    public void saveFPBDocument(RequisitionStatus requisitionStatus, RequisitionEntity requisitionEntity, DocumentType documentType) {
        documentRepository.save(
                new DocumentEntity(
                        requisitionEntity.getMmsCode(),
                        BigInteger.valueOf(requisitionEntity.getId()),
                        documentType,
                        requisitionStatus.toString(),
                        new Timestamp(new Date().getTime()),
                        requisitionEntity.getCreatedBy()
                )
        );
    }

    @Override
    public void saveReverseFPBDocument(RequisitionEntity requisitionEntity, Long reversalId, DocumentType documentType, String createdBy) {
        documentRepository.save(
                new DocumentEntity(
                        requisitionEntity.getMmsCode(),
                        BigInteger.valueOf(reversalId),
                        documentType,
                        requisitionEntity.getRequestStatus().toString(),
                        new Timestamp(new Date().getTime()),
                        createdBy
                )
        );
    }
}
