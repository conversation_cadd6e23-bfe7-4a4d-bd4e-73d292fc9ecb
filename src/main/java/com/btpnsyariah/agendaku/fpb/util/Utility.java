package com.btpnsyariah.agendaku.fpb.util;

import com.btpnsyariah.agendaku.fpb.other.model.NationalHolidayDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
public class Utility {

    private static final String ZERO_VALUE_BIGDECIMAL = "0.0000";
    private static final String ZERO_VALUE_INT = "0";

    private Utility() {
        throw new IllegalStateException();
    }

    private static final TreeMap<Integer, String> map = new TreeMap<>();

    static {
        map.put(1000, "M");
        map.put(900, "CM");
        map.put(500, "D");
        map.put(400, "CD");
        map.put(100, "C");
        map.put(90, "XC");
        map.put(50, "L");
        map.put(40, "XL");
        map.put(10, "X");
        map.put(9, "IX");
        map.put(5, "V");
        map.put(4, "IV");
        map.put(1, "I");
    }

    private static final String TRACE_CODE_FORMAT = "ddHHmmssSSS";

    public static String getStackTrace(Exception e) {
        return e.getStackTrace().length > 0 && e.getStackTrace()[0] != null ? e.getStackTrace()[0].toString() : "No data available";
    }

    public static String getStackTraceMethod(Exception e) {
        return e.getStackTrace().length > 0 && e.getStackTrace()[0] != null ? e.getStackTrace()[0].getMethodName()
                .replaceAll("[^a-zA-Z]", "")
                .replace("lambda","") : "N/A";
    }

    public static String getStackTraceMethodClean(Exception e) {
        return e.getStackTrace().length > 0 && e.getStackTrace()[0] != null ? e.getStackTrace()[0].getMethodName()
                .replaceAll("[^a-zA-Z]", "")
                .replace("lambda","")
                .replaceAll("\\d+", "")
                .replaceAll("(.)([A-Z])", "$1 $2")
                .toLowerCase() : "N/A";
    }

    public static PageImpl paginationMapping(Pageable pageable, List<?> objects) {

        if(pageable.getPageSize() == 20) {
            pageable = new Pageable() {
                @Override
                public int getPageNumber() {
                    return 0;
                }

                @Override
                public int getPageSize() {
                    return objects.size();
                }

                @Override
                public long getOffset() {
                    return 0;
                }

                @Override
                public Sort getSort() {
                    return Sort.unsorted();
                }

                @Override
                public Pageable next() {
                    return null;
                }

                @Override
                public Pageable previousOrFirst() {
                    return null;
                }

                @Override
                public Pageable first() {
                    return null;
                }

                @Override
                public Pageable withPage(int pageNumber) {
                    return null;
                }

                @Override
                public boolean hasPrevious() {
                    return false;
                }
            };
        }

        int start = (int)pageable.getOffset();
        int endLb = (start + pageable.getPageSize()) > objects.size() ? objects.size() : (start + pageable.getPageSize());
        return new PageImpl(objects.subList(start,endLb),
                PageRequest.of(pageable.getPageNumber(), pageable.getPageSize(), pageable.getSort()),
                objects.size());
    }

    public static byte[] getByteDecodedBase64(String input) {
        return java.util.Base64.getDecoder().decode(input);
    }

    public static String getTraceCode() {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(TRACE_CODE_FORMAT);
            return sdf.format(new Date()) + RandomStringUtils.randomAlphanumeric(3);
        } catch (Exception e) {
            return null;
        }
    }

    public static boolean specialCharValidator(Set<String> values) {
        return values
                .stream()
                .allMatch(c -> c.trim().replaceAll(" +", " ").matches("^[a-z|A-Z]+(?: [a-z|A-Z]+)*$"));
    }

    public static boolean specialCharValidator(String value) {
        return value.matches("^[a-z|A-Z]+(?: [a-z|A-Z]+)*$");
    }

    public static String returnExistValues(String value) {
        if(!Arrays.stream(value.split(", ")).allMatch(r -> r.equalsIgnoreCase("null"))){
            return value.replace("null, ","").replace(", null","");
        } else {
            return null;
        }
    }

    public static String convertToRomanNumeral(int number) {
        int l =  map.floorKey(number);
        if ( number == l ) {
            return map.get(number);
        }
        return map.get(l) + convertToRomanNumeral(number-l);
    }

    public static String formatRequisitionNumber(String refNo) {

        //If MMS is HUB this will remove the HUB in the requisition number
        refNo = refNo.replace("HUB","").replace("hub","");

        List<String> refNoArray = Arrays.asList(refNo.split("/"));
        refNoArray.set(0, String.format("%04d", Integer.parseInt(refNoArray.get(0))));
        refNoArray.set(2, convertToRomanNumeral(Integer.parseInt(refNoArray.get(2))));
        return String.join("/", refNoArray);
    }

    public static String getIndonesianCurrencyValue(BigDecimal value) {
        boolean isValueNegative = false;
        if (value.compareTo(BigDecimal.ZERO) < 0) {
            value = value.abs();
            isValueNegative = true;
        }
        String indCurrency = String.valueOf(value);
        if (indCurrency.equalsIgnoreCase(ZERO_VALUE_BIGDECIMAL) || indCurrency.equalsIgnoreCase(ZERO_VALUE_INT)) {
            return "0";
        }
        String convertedNumber = NumberFormat.getNumberInstance(Locale.US).format(value);
        if (isValueNegative) {
            convertedNumber = "-" + convertedNumber;
        }
        return convertedNumber.replace(",", ".");
    }

    public static String getHexValue(String input){
        StringBuilder stringBuffer = new StringBuilder();
        char[] charArray = input.toCharArray();
        for (char c : charArray) {
            String hexString = Integer.toHexString(c);
            stringBuffer.append(hexString);
        }
        return stringBuffer.toString();
    }

    public static long calculateDateDiffExcludeWeekend(Date firstDate, Date secondDate) {
        firstDate = convertUTCToGMT7(firstDate);
        secondDate = convertUTCToGMT7(secondDate);
        long diffInMillies = Math.abs(secondDate.getTime() - firstDate.getTime());
        long diffDays = TimeUnit.DAYS.convert(diffInMillies, TimeUnit.MILLISECONDS);
        int dateDiff = 0;

        for (int i = 0; i < diffDays; i++) {
            Calendar cal = Calendar.getInstance();
            cal.setTime(firstDate);
            cal.add(Calendar.DATE, i);
            if(cal.get(Calendar.DAY_OF_WEEK) != Calendar.SATURDAY && cal.get(Calendar.DAY_OF_WEEK) != Calendar.SUNDAY) {
                dateDiff++;
            }
        }
        return dateDiff;
    }

    public static long calculateDateDiffExcludeWeekendAndNationalHolidays(Date firstDate, Date secondDate, List<NationalHolidayDTO> nationalHolidayList) {
        firstDate = convertUTCToGMT7(firstDate);
        secondDate = convertUTCToGMT7(secondDate);
        long diffInMillies = Math.abs(secondDate.getTime() - firstDate.getTime());
        long diffDays = TimeUnit.DAYS.convert(diffInMillies, TimeUnit.MILLISECONDS);
        int holiday = 0;

        for (int i = 0; i < diffDays; i++) {

            Calendar normalCal = Calendar.getInstance();
            Calendar holidayStartCal = Calendar.getInstance();
            Calendar holidayEndCal = Calendar.getInstance();

            normalCal.setTime(firstDate);
            normalCal.add(Calendar.DATE, i);

            if (normalCal.get(Calendar.DAY_OF_WEEK) == Calendar.SATURDAY || normalCal.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY) {
                holiday++;
            } else if (nationalHolidayList.stream().anyMatch(n -> {
                holidayStartCal.setTime(n.getStartDate());
                holidayEndCal.setTime(n.getEndDate());
                return normalCal.after(holidayStartCal) && normalCal.before(holidayEndCal);
            })) {
                holiday++;
            }
        }
        return diffDays - holiday;
    }

    public static Timestamp convertUTCToGMT7(Date date) {
        return new Timestamp(date.getTime() + (1000 * 60 * 60 * 7));
    }

    public static String getGMT7StringTimestampHourAndMinute() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.HOUR,7);
        return String.format("%02d:%02d", calendar.get(Calendar.HOUR_OF_DAY), calendar.get(Calendar.MINUTE));
    }

    public static Timestamp getTimestamptUTCNow(){
        return new Timestamp(new Date().getTime());
    }

}
