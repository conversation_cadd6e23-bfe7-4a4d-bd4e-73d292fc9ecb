package com.btpnsyariah.agendaku.fpb.catalogue;

import lombok.*;

import javax.persistence.*;
import java.sql.Timestamp;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "FPBCatalogueCategory", schema = "dbo")
public class CatalogueCategoryEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "Id", nullable = false)
    private Long id;

    @Column(name = "CategoryName")
    private String categoryName;

    @Column(name = "CreatedBy")
    private String createdBy;

    @Column(name = "CreatedDate")
    private Timestamp createdDate;

    @Column(name = "UpdatedBy")
    private String updatedBy;

    @Column(name = "UpdatedDate")
    private Timestamp updatedDate;

    public CatalogueCategoryEntity(String categoryName, String createdBy, Timestamp createdDate) {
        this.categoryName = categoryName;
        this.createdBy = createdBy;
        this.createdDate = createdDate;
    }

    public CatalogueCategoryEntity(Long id, String categoryName, String createdBy, Timestamp createdDate) {
        this.id = id;
        this.categoryName = categoryName;
        this.createdBy = createdBy;
        this.createdDate = createdDate;
    }
}
