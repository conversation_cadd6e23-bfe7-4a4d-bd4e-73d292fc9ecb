package com.btpnsyariah.agendaku.fpb.catalogue;

import com.btpnsyariah.agendaku.fpb.exception.BusinessException;
import com.btpnsyariah.agendaku.fpb.model.BaseResponse;
import com.btpnsyariah.agendaku.fpb.util.Utility;
import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

@CrossOrigin(origins = {
        "https://core-agendaku-south.apps.south.syariahbtpn.com",
        "https://core-agendaku-north.apps.north.syariahbtpn.com",
        "http://localhost:3000",
        "http://localhost:3001",
        "https://agendaku.apps.btpnsyariah.com",
        "https://core-agendaku-dirty.apps.nww.syariahbtpn.com",
        "https://core-agendaku-alpha.apps.nww.syariahbtpn.com",
        "https://core-agendaku-beta.apps.nww.syariahbtpn.com",
        "https://core-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com"})
@RestController
@RequestMapping("/item-catalogues")
@Api(tags = "Item catalogues")
public class CatalogueController {

    @Autowired
    private CatalogueService catalogueService;

    private static final Logger LOGGER = LoggerFactory.getLogger(CatalogueController.class);
    

    @Operation(summary = "Get all items in the catalogue", parameters = {
            @Parameter(in = ParameterIn.QUERY
                    , description = "Page you want to retrieve (0..N)"
                    , name = "page"
                    , content = @Content(schema = @Schema(type = "integer", defaultValue = "0"))),
            @Parameter(in = ParameterIn.QUERY
                    , description = "Number of records per page."
                    , name = "size"
                    , content = @Content(schema = @Schema(type = "integer", defaultValue = "20")))
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Items fetched",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) }),
            @ApiResponse(responseCode = "500", description = "Failed to fetch items",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) })
    })
    @GetMapping()
    @CrossOrigin(origins = {
            "https://core-agendaku-south.apps.south.syariahbtpn.com",
            "https://core-agendaku-north.apps.north.syariahbtpn.com",
            "http://localhost:3000",
            "http://localhost:3001",
            "https://agendaku.apps.btpnsyariah.com",
            "https://core-agendaku-dirty.apps.nww.syariahbtpn.com",
            "https://core-agendaku-alpha.apps.nww.syariahbtpn.com",
            "https://core-agendaku-beta.apps.nww.syariahbtpn.com",
            "https://core-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com"})
    public ResponseEntity<BaseResponse> getItemCatalogue(@Parameter(hidden = true) Pageable pageable) {
        try {
            return ResponseEntity.ok(new BaseResponse(
                    true,
                    "Items fetched",
                    catalogueService.getItemCatalogue(pageable)
            ));
        } catch (Exception e) {
            String traceCode = Utility.getTraceCode();
            LOGGER.error("[CatalogueController] [{}] There was an error while fetching items : {} | {}",
                    traceCode,
                    e.getMessage(),
                    Utility.getStackTrace(e));
            return new ResponseEntity<>(new BaseResponse(
                    false,
                    "Failed to fetch items : " + e.getMessage(),
                    traceCode
            ), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Save items to the catalogue")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Items saved!",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) }),
            @ApiResponse(responseCode = "500", description = "Failed to save items",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) })
    })
    @PostMapping
    @CrossOrigin(origins = {
            "https://core-agendaku-south.apps.south.syariahbtpn.com",
            "https://core-agendaku-north.apps.north.syariahbtpn.com",
            "http://localhost:3000",
            "http://localhost:3001",
            "https://agendaku.apps.btpnsyariah.com",
            "https://core-agendaku-dirty.apps.nww.syariahbtpn.com",
            "https://core-agendaku-alpha.apps.nww.syariahbtpn.com",
            "https://core-agendaku-beta.apps.nww.syariahbtpn.com",
            "https://core-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com"})
    public ResponseEntity<BaseResponse> saveItemCatalogue(
            @RequestHeader("UserId") String userId,
            @RequestBody List<CatalogueRequestDTO> catalogueRequestDTOList) {
        try {
            catalogueService.persistItemCatalogue(userId, catalogueRequestDTOList);
            return ResponseEntity.ok(new BaseResponse(
                    true,
                    "Items saved!"
            ));
        } catch (Exception e) {
            String traceCode = Utility.getTraceCode();
            LOGGER.error("[CatalogueController] [{}] There was an error while saving items : {} | {}",
                    traceCode,
                    e.getMessage(),
                    Utility.getStackTrace(e));
            return new ResponseEntity<>(new BaseResponse(
                    false,
                    "Failed to save items : " + e.getMessage(),
                    traceCode
            ), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Save items to the catalogue")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Items saved!",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) }),
            @ApiResponse(responseCode = "500", description = "Failed to save items",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) })
    })
    @PostMapping("/save/batch")
    @CrossOrigin(origins = {
            "https://core-agendaku-south.apps.south.syariahbtpn.com",
            "https://core-agendaku-north.apps.north.syariahbtpn.com",
            "http://localhost:3000",
            "http://localhost:3001",
            "https://agendaku.apps.btpnsyariah.com",
            "https://core-agendaku-dirty.apps.nww.syariahbtpn.com",
            "https://core-agendaku-alpha.apps.nww.syariahbtpn.com",
            "https://core-agendaku-beta.apps.nww.syariahbtpn.com",
            "https://core-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com"})
    public ResponseEntity<BaseResponse> batchSaveItemCatalogue(
            @RequestHeader("UserId") String userId,
            @RequestBody List<CatalogueBatchRequestDTO> catalogueRequestDTOList) {
        try {
            catalogueService.batchPersistItemCatalogue(userId, catalogueRequestDTOList);
            return ResponseEntity.ok(new BaseResponse(
                    true,
                    "Items saved!"
            ));
        } catch (Exception e) {
            String traceCode = Utility.getTraceCode();
            LOGGER.error("[CatalogueController] [{}] There was an error while saving items : {} | {}",
                    traceCode,
                    e.getMessage(),
                    Utility.getStackTrace(e));
            return new ResponseEntity<>(new BaseResponse(
                    false,
                    "Failed to save items : " + e.getMessage(),
                    traceCode
            ), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Update item in the catalogue")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Item updated!",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) }),
            @ApiResponse(responseCode = "404", description = "Item not found",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) }),
            @ApiResponse(responseCode = "500", description = "Failed to update item",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) })
    })
    @PutMapping("/{id}")
    @CrossOrigin(origins = {
            "https://core-agendaku-south.apps.south.syariahbtpn.com",
            "https://core-agendaku-north.apps.north.syariahbtpn.com",
            "http://localhost:3000",
            "http://localhost:3001",
            "https://agendaku.apps.btpnsyariah.com",
            "https://core-agendaku-dirty.apps.nww.syariahbtpn.com",
            "https://core-agendaku-alpha.apps.nww.syariahbtpn.com",
            "https://core-agendaku-beta.apps.nww.syariahbtpn.com",
            "https://core-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com"})
    public ResponseEntity<BaseResponse> updateItemCatalogue(
            @RequestHeader("UserId") String userId,
            @PathVariable("id") Long itemId,
            @RequestBody CatalogueRequestDTO updateRequest) {
        try {
            catalogueService.updateItemCatalogue(userId, itemId, updateRequest);
            return ResponseEntity.ok(new BaseResponse(
                    true,
                    "Item updated!"
            ));
        } catch (BusinessException e) {
            String traceCode = Utility.getTraceCode();
            LOGGER.error("[CatalogueController] [{}] There was an error while updating item : {} | {}",
                    traceCode,
                    e.getMessage(),
                    Utility.getStackTrace(e));
            return new ResponseEntity<>(new BaseResponse(
                    false,
                    "Failed to update item : " + e.getMessage(),
                    traceCode
            ), e.getHttpStatus());
        } catch (Exception e) {
            String traceCode = Utility.getTraceCode();
            LOGGER.error("[CatalogueController] [{}] There was an error while updating item : {} | {}",
                    traceCode,
                    e.getMessage(),
                    Utility.getStackTrace(e));
            return new ResponseEntity<>(new BaseResponse(
                    false,
                    "Failed to update item : " + e.getMessage(),
                    traceCode
            ), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Delete item in the catalogue")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Item deleted!",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) }),
            @ApiResponse(responseCode = "404", description = "Item not found",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) }),
            @ApiResponse(responseCode = "500", description = "Failed to delete item",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) })
    })
    @DeleteMapping("/{id}")
    @CrossOrigin(origins = {
            "https://core-agendaku-south.apps.south.syariahbtpn.com",
            "https://core-agendaku-north.apps.north.syariahbtpn.com",
            "http://localhost:3000",
            "http://localhost:3001",
            "https://agendaku.apps.btpnsyariah.com",
            "https://core-agendaku-dirty.apps.nww.syariahbtpn.com",
            "https://core-agendaku-alpha.apps.nww.syariahbtpn.com",
            "https://core-agendaku-beta.apps.nww.syariahbtpn.com",
            "https://core-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com"})
    public ResponseEntity<BaseResponse> deleteItemCatalogue(
            @RequestHeader("UserId") String userId,
            @PathVariable("id") Long itemId) {
        try {
            catalogueService.deleteItem(userId, itemId);
            return ResponseEntity.ok(new BaseResponse(
                    true,
                    "Item deleted!"
            ));
        } catch (BusinessException e) {
                String traceCode = Utility.getTraceCode();
                LOGGER.error("[CatalogueController] [{}] There was an error while deleting item : {} | {}",
                        traceCode,
                        e.getMessage(),
                        Utility.getStackTrace(e));
                return new ResponseEntity<>(new BaseResponse(
                        false,
                        "Failed to delete item : " + e.getMessage(),
                        traceCode
                ), e.getHttpStatus());
         } catch (Exception e) {
            String traceCode = Utility.getTraceCode();
            LOGGER.error("[CatalogueController] [{}] There was an error while deleting item : {} | {}",
                    traceCode,
                    e.getMessage(),
                    Utility.getStackTrace(e));
            return new ResponseEntity<>(new BaseResponse(
                    false,
                    "Failed to delete item : " + e.getMessage(),
                    traceCode
            ), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Get all categories in the catalogue")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Categories fetched",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) }),
            @ApiResponse(responseCode = "500", description = "Failed to fetch categories",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) })
    })
    @GetMapping("/categories")
    @CrossOrigin(origins = {
            "https://core-agendaku-south.apps.south.syariahbtpn.com",
            "https://core-agendaku-north.apps.north.syariahbtpn.com",
            "http://localhost:3000",
            "http://localhost:3001",
            "https://agendaku.apps.btpnsyariah.com",
            "https://core-agendaku-dirty.apps.nww.syariahbtpn.com",
            "https://core-agendaku-alpha.apps.nww.syariahbtpn.com",
            "https://core-agendaku-beta.apps.nww.syariahbtpn.com",
            "https://core-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com"})
    public ResponseEntity<BaseResponse> getCatalogueCategories() {
        try {
            return ResponseEntity.ok(new BaseResponse(
                    true,
                    "Categories fetched",
                    catalogueService.getCatalogueCategories()
            ));
        } catch (Exception e) {
            String traceCode = Utility.getTraceCode();
            LOGGER.error("[CatalogueController] [{}] There was an error while getting categories : {} | {}",
                    traceCode,
                    e.getMessage(),
                    Utility.getStackTrace(e));
            return new ResponseEntity<>(new BaseResponse(
                    false,
                    "Failed to fetch categories : " + e.getMessage(),
                    traceCode
            ), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Save categories to the catalogue")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Categories saved",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) }),
            @ApiResponse(responseCode = "500", description = "Failed to save categories",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) })
    })
    @PostMapping("categories")
    @CrossOrigin(origins = {
            "https://core-agendaku-south.apps.south.syariahbtpn.com",
            "https://core-agendaku-north.apps.north.syariahbtpn.com",
            "http://localhost:3000",
            "http://localhost:3001",
            "https://agendaku.apps.btpnsyariah.com",
            "https://core-agendaku-dirty.apps.nww.syariahbtpn.com",
            "https://core-agendaku-alpha.apps.nww.syariahbtpn.com",
            "https://core-agendaku-beta.apps.nww.syariahbtpn.com",
            "https://core-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com"})
    public ResponseEntity<BaseResponse> saveCatalogueCategories(
            @RequestHeader("UserId") String userId,
            @RequestBody Set<String> catalogues) {
        try {
            catalogueService.persistCatalogueCategories(userId, catalogues);
            return ResponseEntity.ok(new BaseResponse(
                    true,
                    "Categories saved!"
            ));
        } catch (BusinessException e) {
            String traceCode = Utility.getTraceCode();
            LOGGER.error("[CatalogueController] [{}] There was an error while saving item catalogues : {} | {}",
                    traceCode,
                    e.getMessage(),
                    Utility.getStackTrace(e));
            return new ResponseEntity<>(new BaseResponse(
                    false,
                    "Failed to save categories : " + e.getMessage(),
                    traceCode
            ), e.getHttpStatus());
        } catch (Exception e) {
            String traceCode = Utility.getTraceCode();
            LOGGER.error("[CatalogueController] [{}] There was an error while saving item catalogues : {} | {}",
                    traceCode,
                    e.getMessage(),
                    Utility.getStackTrace(e));
            return new ResponseEntity<>(new BaseResponse(
                    false,
                    "Failed to save categories : " + e.getMessage(),
                    traceCode
            ), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Update category in the catalogue")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Category updated!",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) }),
            @ApiResponse(responseCode = "404", description = "Category not found",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) }),
            @ApiResponse(responseCode = "500", description = "Failed to update category",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) })
    })
    @PutMapping("categories/{categoryId}")
    @CrossOrigin(origins = {
            "https://core-agendaku-south.apps.south.syariahbtpn.com",
            "https://core-agendaku-north.apps.north.syariahbtpn.com",
            "http://localhost:3000",
            "http://localhost:3001",
            "https://agendaku.apps.btpnsyariah.com",
            "https://core-agendaku-dirty.apps.nww.syariahbtpn.com",
            "https://core-agendaku-alpha.apps.nww.syariahbtpn.com",
            "https://core-agendaku-beta.apps.nww.syariahbtpn.com",
            "https://core-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com"})
    public ResponseEntity<BaseResponse> updateCatalogueCategory(
            @RequestHeader("UserId") String userId,
            @PathVariable("categoryId") Long categoryId,
            @RequestBody CatalogueCategoryDTO updateRequest) {
        try {
            catalogueService.updateCatalogueCategory(userId, categoryId, updateRequest);
            return ResponseEntity.ok(new BaseResponse(
                    true,
                    "Category updated!"
            ));
        } catch (BusinessException e) {
            String traceCode = Utility.getTraceCode();
            LOGGER.error("[CatalogueController] [{}] There was an error while updating catalogue category : {} | {}",
                    traceCode,
                    e.getMessage(),
                    Utility.getStackTrace(e));
            return new ResponseEntity<>(new BaseResponse(
                    false,
                    "Failed to update category : " + e.getMessage(),
                    traceCode
            ), e.getHttpStatus());
        } catch (Exception e) {
            String traceCode = Utility.getTraceCode();
            LOGGER.error("[CatalogueController] [{}] There was an error while updating catalogue category : {} | {}",
                    traceCode,
                    e.getMessage(),
                    Utility.getStackTrace(e));
            return new ResponseEntity<>(new BaseResponse(
                    false,
                    "Failed to update category : " + e.getMessage(),
                    traceCode
            ), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Delete category in the catalogue")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Category deleted!",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) }),
            @ApiResponse(responseCode = "404", description = "Category not found",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) }),
            @ApiResponse(responseCode = "500", description = "Failed to delete Category",
                    content = { @Content(mediaType = "application/json",
                            schema = @Schema(implementation = BaseResponse.class)) })
    })
    @DeleteMapping("categories/{id}")
    @CrossOrigin(origins = {
            "https://core-agendaku-south.apps.south.syariahbtpn.com",
            "https://core-agendaku-north.apps.north.syariahbtpn.com",
            "http://localhost:3000",
            "http://localhost:3001",
            "https://agendaku.apps.btpnsyariah.com",
            "https://core-agendaku-dirty.apps.nww.syariahbtpn.com",
            "https://core-agendaku-alpha.apps.nww.syariahbtpn.com",
            "https://core-agendaku-beta.apps.nww.syariahbtpn.com",
            "https://core-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com"})
    public ResponseEntity<BaseResponse> deleteCatalogueCategory(
            @RequestHeader("UserId") String userId,
            @PathVariable("id") Long categoryId) {
        try {
            catalogueService.deleteCategory(userId, categoryId);
            return ResponseEntity.ok(new BaseResponse(
                    true,
                    "Category deleted!"
            ));
        } catch (BusinessException e) {
                String traceCode = Utility.getTraceCode();
                LOGGER.error("[CatalogueController] [{}] There was an error while deleting category : {} | {}",
                        traceCode,
                        e.getMessage(),
                        Utility.getStackTrace(e));
                return new ResponseEntity<>(new BaseResponse(
                        false,
                        "Failed to delete category : " + e.getMessage(),
                        traceCode
                ), e.getHttpStatus());
         } catch (Exception e) {
            String traceCode = Utility.getTraceCode();
            LOGGER.error("[CatalogueController] [{}] There was an error while deleting category : {} | {}",
                    traceCode,
                    e.getMessage(),
                    Utility.getStackTrace(e));
            return new ResponseEntity<>(new BaseResponse(
                    false,
                    "Failed to delete category : " + e.getMessage(),
                    traceCode
            ), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

}
