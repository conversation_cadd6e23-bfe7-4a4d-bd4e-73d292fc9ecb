package com.btpnsyariah.agendaku.fpb.catalogue;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CatalogueRequestDTO {

    private Long category;
    private ItemLifespan lifespan;
    private String itemName;
    private String specifications;
    private String measurementUnit;
    private BigDecimal price;

}
