package com.btpnsyariah.agendaku.fpb.catalogue;

import com.btpnsyariah.agendaku.fpb.exception.BusinessException;
import com.btpnsyariah.agendaku.fpb.model.ResponseMessage;
import com.btpnsyariah.agendaku.fpb.transaction.requisition.RequisitionEntity;
import com.btpnsyariah.agendaku.fpb.util.Utility;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringEscapeUtils;
import org.apache.commons.text.WordUtils;
import org.apache.commons.text.similarity.JaroWinklerDistance;
import org.apache.commons.text.similarity.LevenshteinDistance;
import org.apache.poi.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CatalogueServiceImpl implements CatalogueService {

    @Autowired
    private CatalogueRepository catalogueRepository;

    @Autowired
    private CatalogueCategoryRepository catalogueCategoryRepository;
    @Override
    public CatalogueEntity findCatalogueById(Long catalogueId) throws BusinessException {
        return catalogueRepository.findById(catalogueId).orElseThrow(() -> new BusinessException(HttpStatus.NOT_FOUND, ResponseMessage.CATALOGUE_NOT_FOUND));
    }

    @Override
    public List<CatalogueEntity> findCatalogueByIds(List<Long> ids) {
        return catalogueRepository.findCatalogueByIds(ids);
    }

    @Override
    public List<CatalogueEntity> findAllCatalogues() {
        return catalogueRepository.findAll();
    }

    @Override
    public void batchPersistItemCatalogue(String userId, List<CatalogueBatchRequestDTO> catalogueRequestDTOList) {

        List<CatalogueEntity> catalogues = new ArrayList<>();
        JaroWinklerDistance jaroWinklerDistance = new JaroWinklerDistance();

        //Soft delete all previous items
        List<CatalogueEntity> catalogueEntities = findAllCatalogues();
        catalogueEntities.forEach(c -> c.setDeleted(true));
        catalogueRepository.saveAll(catalogueEntities);

        //Get catalogue version
        Optional<CatalogueEntity> latestCatalogue = catalogueEntities.stream().max(Comparator.comparing(CatalogueEntity::getCreatedDate));
        long catalogueVersion = latestCatalogue.isPresent() && latestCatalogue.get().getCatalogueVersion() != null ? latestCatalogue.get().getCatalogueVersion() : 0;

        List<CatalogueCategoryEntity> catalogueCategoryEntities = getCatalogueCategories();

        catalogueRequestDTOList.forEach(r -> {
            if(catalogueCategoryEntities.stream().noneMatch(c -> jaroWinklerDistance.apply(
                    c.getCategoryName().trim().replace(" ","").toLowerCase(),
                    r.getCategory().trim().replace(" ","").toLowerCase()) < 0.12)) {

                CatalogueCategoryEntity category = catalogueCategoryRepository.save(CatalogueCategoryEntity.builder()
                        .categoryName(r.getCategory().trim())
                        .createdBy(userId)
                        .createdDate(new Timestamp(new Date().getTime()))
                        .build());

                catalogues.add(CatalogueEntity.builder()
                        .category(category.getId())
                        .lifespan(r.getLifespan())
                        .itemName(r.getItemName())
                        .specifications(r.getSpecifications())
                        .measurementUnit(r.getMeasurementUnit())
                        .price(r.getPrice())
                        .createdBy(userId)
                        .createdDate(new Timestamp(new Date().getTime()))
                        .catalogueVersion(catalogueVersion != 0 ? catalogueVersion + 1 : 1)
                        .build());

            } else {
                catalogues.add(CatalogueEntity.builder()
                        .category(catalogueCategoryEntities.stream().filter(c -> jaroWinklerDistance.apply(
                                c.getCategoryName().trim().replace(" ","").toLowerCase(),
                                r.getCategory().trim().replace(" ","").toLowerCase()) <= 0.12).findFirst().get().getId())
                        .lifespan(r.getLifespan())
                        .itemName(r.getItemName())
                        .specifications(r.getSpecifications())
                        .measurementUnit(r.getMeasurementUnit())
                        .price(r.getPrice())
                        .createdBy(userId)
                        .createdDate(new Timestamp(new Date().getTime()))
                        .catalogueVersion(catalogueVersion != 0 ? catalogueVersion + 1 : 1)
                        .build());
            }
        });
        catalogueRepository.saveAll(catalogues);
    }

    @Override
    public PageImpl getItemCatalogue(Pageable pageable) {
        return Utility.paginationMapping(pageable, catalogueRepository.findAllCatalogues().stream().map(
                c -> new CatalogueResponseDTO(
                        c.getId(),
                        c.getCategory(),
                        c.getLifespan().getItemLifespanValue(),
                        c.getItemName(),
                        c.getSpecifications(),
                        c.getMeasurementUnit(),
                        c.getPrice(),
                        c.getCreatedBy(),
                        c.getCreatedDate(),
                        c.getUpdatedDate(),
                        c.getUpdatedBy()
                )
        ).collect(Collectors.toList()));
    }

    @Override
    public void persistItemCatalogue(String userId, List<CatalogueRequestDTO> catalogueRequestDTOList) {
        catalogueRepository.saveAll(
                catalogueRequestDTOList
                        .stream().map(c -> new CatalogueEntity(
                                c.getCategory(),
                                c.getLifespan(),
                                c.getItemName(),
                                c.getSpecifications(),
                                c.getMeasurementUnit(),
                                c.getPrice(),
                                userId,
                                new Timestamp(new Date().getTime())
                        )).collect(Collectors.toList())
        );
    }

    @Override
    public void updateItemCatalogue(String userId, Long itemId, CatalogueRequestDTO updateRequest) throws BusinessException {
        Optional<CatalogueEntity> optionalCatalogueEntity = catalogueRepository.findById(itemId);

        if(optionalCatalogueEntity.isPresent()) {
            CatalogueEntity catalogueEntity = optionalCatalogueEntity.get();

            catalogueEntity.setCategory(updateRequest.getCategory());
            catalogueEntity.setLifespan(updateRequest.getLifespan());
            catalogueEntity.setItemName(updateRequest.getItemName());
            catalogueEntity.setSpecifications(updateRequest.getSpecifications());
            catalogueEntity.setMeasurementUnit(updateRequest.getMeasurementUnit());
            catalogueEntity.setPrice(updateRequest.getPrice());

            catalogueRepository.save(catalogueEntity);
        } else {
            throw new BusinessException(HttpStatus.NOT_FOUND, "Item not found with id : " + itemId);
        }
    }

    @Override
    public void deleteItem(String userId, Long itemId) throws BusinessException {

        Optional<CatalogueEntity> optionalCatalogueEntity = catalogueRepository.findById(itemId);

        if(optionalCatalogueEntity.isPresent()) {
            catalogueRepository.deleteById(itemId);
        } else {
            throw new BusinessException(HttpStatus.NOT_FOUND, "Item not found with id : " + itemId);
        }

    }

    @Override
    public List<CatalogueCategoryEntity> getCatalogueCategories() {
        return catalogueCategoryRepository.findAll();
    }

    @Override
    public void persistCatalogueCategories(String userId, Set<String> catalogues) throws BusinessException {

        validateCategory(catalogues);

        catalogueCategoryRepository.saveAll(
                catalogues
                        .stream().map(c -> new CatalogueCategoryEntity(
                                WordUtils.capitalizeFully(c.trim().replaceAll(" +", " ")),
                                userId,
                                new Timestamp(new Date().getTime())
                        )).collect(Collectors.toList())
        );
    }

    @Override
    public void updateCatalogueCategory(String userId, Long categoryId, CatalogueCategoryDTO updateRequest) throws BusinessException {

        if(Utility.specialCharValidator(updateRequest.getCategoryName())){
            Optional<CatalogueCategoryEntity> optionalCatalogueCategoryEntity = catalogueCategoryRepository.findById(categoryId);

            if(optionalCatalogueCategoryEntity.isPresent()) {
                CatalogueCategoryEntity catalogueCategoryEntity = optionalCatalogueCategoryEntity.get();

                catalogueCategoryEntity.setCategoryName(updateRequest.getCategoryName());
                catalogueCategoryEntity.setUpdatedBy(userId);
                catalogueCategoryEntity.setUpdatedDate(new Timestamp(new Date().getTime()));

                catalogueCategoryRepository.save(catalogueCategoryEntity);
            } else {
                throw new BusinessException(HttpStatus.NOT_FOUND, "Category not found with id : " + categoryId);
            }
        } else {
            throw new BusinessException(HttpStatus.BAD_REQUEST, "Characters and numbers are not allowed!");
        }
    }

    @Override
    public void deleteCategory(String userId, Long categoryId) throws BusinessException {
        Optional<CatalogueCategoryEntity> optionalCatalogueCategoryEntity = catalogueCategoryRepository.findById(categoryId);

        if(optionalCatalogueCategoryEntity.isPresent()) {
            catalogueCategoryRepository.deleteById(categoryId);
        } else {
            throw new BusinessException(HttpStatus.NOT_FOUND, "Category not found with id : " + categoryId);
        }
    }

    private void validateCategory(Set<String> catalogues) throws BusinessException {
        if(Utility.specialCharValidator(catalogues)){
            String categoryExist = categoryExist(catalogues);
            if(categoryExist != null) {
                throw new BusinessException(HttpStatus.CONFLICT, categoryExist + " already exist!");
            }
        } else {
            throw new BusinessException(HttpStatus.BAD_REQUEST, "Characters and numbers are not allowed!");
        }
    }

    private String categoryExist(Set<String> catalogues) {
        List<CatalogueCategoryEntity> categoryList = catalogueCategoryRepository.findAll();
        return Utility.returnExistValues(catalogues
                .stream()
                .map(c -> {
                    if(categoryList
                            .stream()
                            .map(d -> d.getCategoryName().toLowerCase().trim().replace(" ", ""))
                            .collect(Collectors.toSet()).contains(c.toLowerCase().trim().replace(" ", ""))){
                        return c;
                    } else {
                        return null;
                    }
                }).collect(Collectors.joining(", ")));
    }
}
