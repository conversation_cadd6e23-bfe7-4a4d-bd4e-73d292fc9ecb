package com.btpnsyariah.agendaku.fpb.catalogue;

import lombok.*;

import javax.persistence.*;
import java.math.BigDecimal;
import java.sql.Timestamp;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "FPBItemCatalogue", schema = "dbo")
public class CatalogueEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "Id", nullable = false)
    private Long id;

    @Column(name = "Category")
    private Long category;

    @Enumerated(EnumType.STRING)
    @Column(name = "Lifespan")
    private ItemLifespan lifespan;

    @Column(name = "ItemName")
    private String itemName;

    @Column(name = "Specifications")
    private String specifications;

    @Column(name = "MeasurementUnit")
    private String measurementUnit;

    @Column(name = "Price")
    private BigDecimal price;

    @Column(name = "CreatedBy")
    private String createdBy;

    @Column(name = "CreatedDate")
    private Timestamp createdDate;

    @Column(name = "UpdatedBy")
    private String updatedBy;

    @Column(name = "UpdatedDate")
    private Timestamp updatedDate;

    @Column(name = "IsDeleted")
    private boolean isDeleted;

    @Column(name = "CatalogueVersion")
    private Long catalogueVersion;

    public String getLifespan() {
        return lifespan.getItemLifespanValue();
    }

    public void setLifespan(ItemLifespan lifespan) {
        this.lifespan = lifespan;
    }

    public CatalogueEntity(Long category, ItemLifespan lifespan, String itemName, String specifications, String measurementUnit, BigDecimal price, String createdBy, Timestamp createdDate) {
        this.category = category;
        this.lifespan = lifespan;
        this.itemName = itemName;
        this.specifications = specifications;
        this.measurementUnit = measurementUnit;
        this.price = price;
        this.createdBy = createdBy;
        this.createdDate = createdDate;
    }

    public CatalogueEntity(Long id, Long category, ItemLifespan lifespan, String itemName, String specifications, String measurementUnit, BigDecimal price, String createdBy, Timestamp createdDate) {
        this.id = id;
        this.category = category;
        this.lifespan = lifespan;
        this.itemName = itemName;
        this.specifications = specifications;
        this.measurementUnit = measurementUnit;
        this.price = price;
        this.createdBy = createdBy;
        this.createdDate = createdDate;
    }

    public CatalogueEntity(Long id, Long category, ItemLifespan lifespan, String itemName, String specifications, String measurementUnit, BigDecimal price, String createdBy, Timestamp createdDate, String updatedBy, Timestamp updatedDate) {
        this.id = id;
        this.category = category;
        this.lifespan = lifespan;
        this.itemName = itemName;
        this.specifications = specifications;
        this.measurementUnit = measurementUnit;
        this.price = price;
        this.createdBy = createdBy;
        this.createdDate = createdDate;
        this.updatedBy = updatedBy;
        this.updatedDate = updatedDate;
    }
}
