package com.btpnsyariah.agendaku.fpb.catalogue;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.sql.Timestamp;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CatalogueResponseDTO {

    private Long id;
    private String category;
    private String lifespan;
    private String itemName;
    private String specifications;
    private String measurementUnit;
    private BigDecimal price;
    private String createdBy;
    private Timestamp createdDate;
    private Timestamp updatedDate;
    private String updatedBy;

}
