package com.btpnsyariah.agendaku.fpb.catalogue;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CatalogueRepository extends JpaRepository<CatalogueEntity, Long> {

    @Query(nativeQuery = true, value = "SELECT\n" +
            "\tfc.Id,\n" +
            "\tfc2.CategoryName AS Category,\n" +
            "\tfc.Lifespan,\n" +
            "\tfc.ItemName,\n" +
            "\tfc.Specifications,\n" +
            "\tfc.MeasurementUnit,\n" +
            "\tfc.Price,\n" +
            "\tfc.CreatedBy,\n" +
            "\tfc.CreatedDate,\n" +
            "\tfc.UpdatedDate,\n" +
            "\tfc.UpdatedBy\n" +
            "FROM\n" +
            "\tFPBItemCatalogue fc\n" +
            "INNER JOIN FPBCatalogueCategory fc2 \n" +
            "ON\n" +
            "\tfc2.Id = fc.Category WHERE fc.IsDeleted = 0")
    List<CatalogueDTO> findAllCatalogues();

    @Query(nativeQuery = true, value = "SELECT * FROM FPBItemCatalogue WHERE Id IN :ids")
    List<CatalogueEntity> findCatalogueByIds(List<Long> ids);
}
