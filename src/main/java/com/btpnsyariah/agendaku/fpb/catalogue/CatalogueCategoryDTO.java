package com.btpnsyariah.agendaku.fpb.catalogue;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CatalogueCategoryDTO {

    private String categoryName;

    private String createdBy;

    private Timestamp createdDate;

    private String updatedBy;

    private Timestamp updatedDate;

    public CatalogueCategoryDTO(String categoryName, String createdBy, Timestamp createdDate) {
        this.categoryName = categoryName;
        this.createdBy = createdBy;
        this.createdDate = createdDate;
    }
}
