package com.btpnsyariah.agendaku.fpb.catalogue;

import com.btpnsyariah.agendaku.fpb.exception.BusinessException;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

public interface CatalogueService {

    PageImpl getItemCatalogue(Pageable pageable);

    void persistItemCatalogue(String userId, List<CatalogueRequestDTO> catalogueRequestDTOList);

    void updateItemCatalogue(String userId, Long itemId, CatalogueRequestDTO updateRequest) throws BusinessException;

    void deleteItem(String userId, Long itemId) throws BusinessException;

    List<CatalogueCategoryEntity> getCatalogueCategories();

    void persistCatalogueCategories(String userId, Set<String> catalogues) throws BusinessException;

    void updateCatalogueCategory(String userId, Long itemId, CatalogueCategoryDTO updateRequest) throws BusinessException;

    void deleteCategory(String userId, Long itemId) throws BusinessException;

    CatalogueEntity findCatalogueById(Long catalogueId) throws BusinessException;

    List<CatalogueEntity> findCatalogueByIds(List<Long> collect);

    List<CatalogueEntity> findAllCatalogues();

    @Transactional(propagation = Propagation.REQUIRES_NEW, isolation = Isolation.READ_UNCOMMITTED, rollbackFor = {Exception.class})
    void batchPersistItemCatalogue(String userId, List<CatalogueBatchRequestDTO> catalogueRequestDTOList);
}
