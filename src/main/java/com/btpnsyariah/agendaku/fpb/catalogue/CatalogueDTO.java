package com.btpnsyariah.agendaku.fpb.catalogue;

import java.math.BigDecimal;
import java.sql.Timestamp;

public interface CatalogueDTO {

    Long getId();
    String getCategory();
    ItemLifespan getLifespan();
    String getItemName();
    String getSpecifications();
    String getMeasurementUnit();
    BigDecimal getPrice();
    String getCreatedBy();
    Timestamp getCreatedDate();
    Timestamp getUpdatedDate();
    String getUpdatedBy();
    
}
