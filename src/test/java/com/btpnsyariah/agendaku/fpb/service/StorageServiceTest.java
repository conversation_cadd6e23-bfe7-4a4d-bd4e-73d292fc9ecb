package com.btpnsyariah.agendaku.fpb.service;

import org.apache.commons.lang3.RandomStringUtils;
import org.codehaus.jackson.map.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@ActiveProfiles("dev")
class StorageServiceTest {

    @Autowired
    private MockMvc mockMvc;

    @Mock
    RestTemplate restTemplate;

    @InjectMocks
    private StorageServiceImpl storageService;

    private final ObjectMapper mapper = new ObjectMapper();

    private ResponseEntity responseEntity;

    private MultipartFile multipartFile;

    private String token;

    private String fileName;

    private HttpMethod requestType;

    private MediaType mediaType;

    @BeforeEach
    public void setUp() {
        multipartFile = new MultipartFile() {
            @Override
            public String getName() {
                return null;
            }

            @Override
            public String getOriginalFilename() {
                return null;
            }

            @Override
            public String getContentType() {
                return null;
            }

            @Override
            public boolean isEmpty() {
                return false;
            }

            @Override
            public long getSize() {
                return 0;
            }

            @Override
            public byte[] getBytes() throws IOException {
                return new byte[0];
            }

            @Override
            public InputStream getInputStream() throws IOException {
                return null;
            }

            @Override
            public void transferTo(File dest) throws IOException, IllegalStateException {

            }
        };
        token = RandomStringUtils.randomAlphanumeric(5);
        fileName = RandomStringUtils.randomAlphanumeric(5);
    }

    @Test
    void sendFileToStorage_ReturnSuccess() throws IOException {

        responseEntity = new ResponseEntity(HttpStatus.OK);

        when(restTemplate.exchange(
                anyString(),
                any(HttpMethod.class),
                any(HttpEntity.class),
                ArgumentMatchers.<Class<String>>any()
        )).thenReturn(responseEntity);

        ResponseEntity response = storageService.send(multipartFile, token, fileName, HttpMethod.PUT, MediaType.IMAGE_PNG);

        assertThat(response.getStatusCode()).isSameAs(HttpStatus.OK);

    }

    @Test
    void getFileFromStorage_ReturnSuccess() {

        responseEntity = new ResponseEntity(new Byte[0], HttpStatus.OK);

        when(restTemplate.exchange(
                anyString(),
                any(HttpMethod.class),
                any(HttpEntity.class),
                ArgumentMatchers.<Class<String>>any()
        )).thenReturn(responseEntity);

        ResponseEntity response = storageService.send(token, fileName, HttpMethod.GET);

        assertThat(response.getStatusCode()).isSameAs(HttpStatus.OK);


    }

    @Test
    void deleteFileFromStorage_ReturnSuccess() {

        responseEntity = new ResponseEntity(HttpStatus.OK);

        when(restTemplate.exchange(
                anyString(),
                any(HttpMethod.class),
                any(HttpEntity.class),
                ArgumentMatchers.<Class<String>>any()
        )).thenReturn(responseEntity);

        ResponseEntity response = storageService.send(token, fileName, HttpMethod.DELETE);

        assertThat(response.getStatusCode()).isSameAs(HttpStatus.OK);
    }

}
