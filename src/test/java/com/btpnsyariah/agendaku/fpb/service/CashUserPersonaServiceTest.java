package com.btpnsyariah.agendaku.fpb.service;

import com.btpnsyariah.agendaku.fpb.user.CashUserPersonaImpl;
import com.btpnsyariah.agendaku.fpb.user.CashUserPersonaService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.context.ActiveProfiles;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
@ActiveProfiles("dev")
class CashUserPersonaServiceTest {

    @InjectMocks
    private CashUserPersonaImpl cashUserPersonaService;

    private final String userId = "MIT012";

    @Test
    void testValidateUserPrivileges_ReturnTrue() {
        assertThat(cashUserPersonaService.validateUserPrivileges(userId, userId)).isTrue();
    }

    @Test
    void testValidateUserPrivileges_ReturnAdminTrue() {
        assertThat(cashUserPersonaService.validateUserPrivileges(userId, "22141938")).isTrue();
    }

}
