package com.btpnsyariah.agendaku.fpb.transaction.item;

import com.btpnsyariah.agendaku.fpb.catalogue.CatalogueEntity;
import com.btpnsyariah.agendaku.fpb.catalogue.ItemLifespan;
import com.btpnsyariah.agendaku.fpb.exception.BusinessException;
import com.btpnsyariah.agendaku.fpb.model.ResponseMessage;
import com.btpnsyariah.agendaku.fpb.service.LoggingService;
import com.btpnsyariah.agendaku.fpb.transaction.items.*;
import com.btpnsyariah.agendaku.fpb.transaction.requisition.*;
import org.apache.commons.lang3.RandomStringUtils;
import org.codehaus.jackson.map.ObjectMapper;
import org.hibernate.exception.JDBCConnectionException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest(ItemController.class)
@AutoConfigureMockMvc(addFilters = false)
@ExtendWith(MockitoExtension.class)
@ActiveProfiles("dev")
class ItemControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    Logger logger;

    @MockBean
    private ItemServiceImpl itemService;

    @MockBean
    private RequisitionService requisitionService;

    @MockBean
    private LoggingService loggingService;

    private final String mmsCode = "W0477";

    private final String note = "note";

    private final String userId = "MIT012";

    private final String userName = "Isma Hariani";

    private final String invoiceId = "15084723835nBU-452";

    private final BigDecimal amount = new BigDecimal("1221250.0000");

    private final String requisitionNo = RandomStringUtils.randomAlphanumeric(15);

    private final ObjectMapper mapper = new ObjectMapper();

    private final List<RequisitionListDTO> requisitionList = new ArrayList<>();

    private RequisitionEntity requisitionEntity = new RequisitionEntity();

    private CatalogueEntity catalogueEntity = new CatalogueEntity();

    private final List<RequisitionItemListDTO> requisitionItemList = new ArrayList<>();

    private final RequisitionApprovalDTO requisitionApprovalDTO = new RequisitionApprovalDTO(
            1L,
            RequisitionStatus.REQUEST_APPROVED_BM,
            ""
    );

    @BeforeEach
    public void setUp() {
        catalogueEntity = new CatalogueEntity();

        requisitionList.add(
                new RequisitionListDTO(
                        1L,
                        requisitionNo,
                        new Timestamp(new Date().getTime()),
                        new Timestamp(new Date().getTime()),
                        userId,
                        userName,
                        new BigDecimal("1221250.0000"),
                        new BigDecimal("1221250.0000"),
                        RequisitionStatus.SUBMITTED,
                        note
                )
        );

        requisitionEntity = new RequisitionEntity();

        RequisitionItemEntity requisitionItemEntity = new RequisitionItemEntity();

        requisitionItemEntity = new RequisitionItemEntity(
                1L,
                requisitionEntity,
                1L,
                2,
                new Timestamp(new Date().getTime()),
                PaymentStatus.CREATED,
                amount,
                amount,
                null,
                null
        );

        requisitionEntity = new RequisitionEntity(
                1L,
                requisitionNo,
                mmsCode,
                userId,
                new Timestamp(new Date().getTime()),
                amount,
                RequisitionStatus.SUBMITTED,
                "",
                "",
                "",
                "",
                new HashSet<>(List.of(requisitionItemEntity)));

        requisitionItemList.add(
                new RequisitionItemListDTO(
                        1L,
                        "Spring Bed",
                        "Furniture",
                        1,
                        "Unit",
                        ItemLifespan.ABOVE_1_YEAR.getItemLifespanValue(),
                        amount,
                        amount,
                        PaymentStatus.CREATED,
                        new HashSet<>(List.of(RandomStringUtils.randomAlphanumeric(5)))
                )
        );
    }

    @Test
    void getRequisitionItemList_ReturnSuccess() throws Exception {

        when(itemService.getRequisitionItemList(anyLong())).thenReturn(requisitionItemList);

        mockMvc.perform(MockMvcRequestBuilders
                        .get("/requisitions/items")
                        .param("requisitionId", "1")
                        .characterEncoding("utf-8"))
                .andDo(print())
                .andExpect(status().is2xxSuccessful())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("Requisition item list fetched"))
                .andExpect(jsonPath("$.data[0].itemName").value(requisitionItemList.get(0).getItemName()))
                .andExpect(jsonPath("$.data[0].itemCategory").value(requisitionItemList.get(0).getItemCategory()))
                .andExpect(jsonPath("$.data[0].itemQty").value(requisitionItemList.get(0).getItemQty()));
    }

    @Test
    void getRequisitionItemList_ThrowsNotFound() throws Exception {

        when(itemService.getRequisitionItemList(anyLong())).thenThrow(new BusinessException(HttpStatus.NOT_FOUND, "Requisition with id 1 not found"));

        mockMvc.perform(MockMvcRequestBuilders
                        .get("/requisitions/items")
                        .param("requisitionId", "1")
                        .characterEncoding("utf-8"))
                .andDo(print())
                .andExpect(status().is4xxClientError())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").exists());
    }

    @Test
    void getRequisitionItemList_ReturnGenericException() throws Exception {

        when(itemService.getRequisitionItemList(anyLong())).thenThrow(JDBCConnectionException.class);

        mockMvc.perform(MockMvcRequestBuilders
                        .get("/requisitions/items")
                        .param("requisitionId", "1")
                        .characterEncoding("utf-8"))
                .andDo(print())
                .andExpect(status().is5xxServerError())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").exists());
    }

    @Test
    void cancelAllRequisitionItem_ReturnSuccess() throws Exception {

        mockMvc.perform(MockMvcRequestBuilders
                        .patch("/requisitions/items/cancel/1")
                        .header("UserId", userId)
                        .param("cancelAll", "true")
                        .characterEncoding("utf-8"))
                .andDo(print())
                .andExpect(status().is2xxSuccessful());

    }

    @Test
    void cancelAllRequisitionItem_ReturnBadRequest() throws Exception {

        mockMvc.perform(MockMvcRequestBuilders
                        .patch("/requisitions/items/cancel/1")
                        .header("UserId", userId)
                        .characterEncoding("utf-8"))
                .andDo(print())
                .andExpect(status().is4xxClientError());

    }

    @Test
    void cancelRequisitionItem_ReturnSuccess() throws Exception {

        mockMvc.perform(MockMvcRequestBuilders
                        .patch("/requisitions/items/cancel/1")
                        .header("UserId", userId)
                        .param("cancelAll", "false")
                        .param("itemId", "1")
                        .characterEncoding("utf-8"))
                .andDo(print())
                .andExpect(status().is2xxSuccessful());

    }

    @Test
    void cancelRequisitionItem_ReturnStatusNotMatch() throws Exception {

        doThrow(new BusinessException(HttpStatus.BAD_REQUEST, ResponseMessage.STATUS_NOT_MATCH)).when(itemService).cancelItem(anyBoolean(), anyLong(), anyLong(), anyString());

        mockMvc.perform(MockMvcRequestBuilders
                        .patch("/requisitions/items/cancel/1")
                        .header("UserId", userId)
                        .param("cancelAll", "false")
                        .param("itemId", "1")
                        .characterEncoding("utf-8"))
                .andDo(print())
                .andExpect(status().is4xxClientError());

    }

    @Test
    void cancelRequisitionItem_ReturnGenericException() throws Exception {

        doThrow(JDBCConnectionException.class).when(itemService).cancelItem(anyBoolean(), anyLong(), anyLong(), anyString());

        mockMvc.perform(MockMvcRequestBuilders
                        .patch("/requisitions/items/cancel/1")
                        .header("UserId", userId)
                        .param("cancelAll", "false")
                        .param("itemId", "1")
                        .characterEncoding("utf-8"))
                .andDo(print())
                .andExpect(status().is5xxServerError());

    }

    @Test
    void utilizePayment_ReturnSuccess() throws Exception {

        mockMvc.perform(MockMvcRequestBuilders
                        .patch("/requisitions/items/payment/utilize/1/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(mapper.writeValueAsString(amount))
                        .characterEncoding("utf-8"))
                .andDo(print())
                .andExpect(status().is2xxSuccessful());

    }

    @Test
    void utilizePayment_ReturnNotFound() throws Exception {

        doThrow(new BusinessException(HttpStatus.NOT_FOUND, ResponseMessage.REQUISITION_NOT_FOUND)).when(itemService).utilizePayment(anyLong(), anyLong(), any());

        mockMvc.perform(MockMvcRequestBuilders
                        .patch("/requisitions/items/payment/utilize/1/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(mapper.writeValueAsString(amount))
                        .characterEncoding("utf-8"))
                .andDo(print())
                .andExpect(status().is4xxClientError());

    }

    @Test
    void utilizePayment_ReturnGenericException() throws Exception {

        doThrow(JDBCConnectionException.class).when(itemService).utilizePayment(anyLong(), anyLong(), any());

        mockMvc.perform(MockMvcRequestBuilders
                        .patch("/requisitions/items/payment/utilize/1/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(mapper.writeValueAsString(amount))
                        .characterEncoding("utf-8"))
                .andDo(print())
                .andExpect(status().is5xxServerError());

    }
}
