package com.btpnsyariah.agendaku.fpb.transaction.requisition;

import com.btpnsyariah.agendaku.fpb.catalogue.ItemLifespan;
import com.btpnsyariah.agendaku.fpb.exception.BusinessException;
import com.btpnsyariah.agendaku.fpb.model.ResponseMessage;
import com.btpnsyariah.agendaku.fpb.service.LoggingService;
import com.btpnsyariah.agendaku.fpb.transaction.items.PaymentStatus;
import com.btpnsyariah.agendaku.fpb.transaction.items.RequisitionItemEntity;
import com.btpnsyariah.agendaku.fpb.transaction.items.RequisitionItemListDTO;
import org.apache.commons.lang3.RandomStringUtils;
import org.codehaus.jackson.map.ObjectMapper;
import org.hibernate.exception.JDBCConnectionException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest(RequisitionController.class)
@AutoConfigureMockMvc(addFilters = false)
@ExtendWith(MockitoExtension.class)
@ActiveProfiles("dev")
class RequisitionControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    Logger logger;

    @MockBean
    private RequisitionServiceImpl requisitionService;

    @MockBean
    private LoggingService loggingService;

    private final String mmsCode = "W0477";

    private final String note = "note";

    private final String userId = "MIT012";

    private final String userName = "Isma Hariani";

    private final String invoiceId = "15084723835nBU-452";

    private final BigDecimal amount = new BigDecimal("1221250.0000");

    private final String deviceId = "00000";

    private final String requisitionNo = RandomStringUtils.randomAlphanumeric(15);

    private final String coCode = RandomStringUtils.randomAlphanumeric(4);

    private final String accessToken = RandomStringUtils.randomAlphanumeric(15);

    private final String rrn = RandomStringUtils.randomAlphanumeric(10);

    private final ObjectMapper mapper = new ObjectMapper();

    private final List<RequisitionListDTO> requisitionList = new ArrayList<>();

    private RequisitionStatusAndListDTO requisitionStatusAndListDTO = new RequisitionStatusAndListDTO();

    private RequisitionEntity requisitionEntity = new RequisitionEntity();

    private final List<RequisitionItemListDTO> requisitionItemList = new ArrayList<>();

    private final RequisitionApprovalDTO requisitionApprovalDTO = new RequisitionApprovalDTO(
            1L,
            RequisitionStatus.REQUEST_APPROVED_BM,
            ""
    );

    private final long[] cancelledItemIds = {1,2,3};

    @BeforeEach
    public void setUp() {
        requisitionList.add(
                new RequisitionListDTO(
                        1L,
                        requisitionNo,
                        new Timestamp(new Date().getTime()),
                        new Timestamp(new Date().getTime()),
                        userId,
                        userName,
                        new BigDecimal("1221250.0000"),
                        new BigDecimal("1221250.0000"),
                        RequisitionStatus.SUBMITTED,
                        note
                )
        );

        requisitionEntity = new RequisitionEntity();

        RequisitionItemEntity requisitionItemEntity = new RequisitionItemEntity();

        requisitionItemEntity = new RequisitionItemEntity(
                1L,
                requisitionEntity,
                1L,
                2,
                new Timestamp(new Date().getTime()),
                PaymentStatus.CREATED,
                amount,
                amount,
                null,
                null
        );

        requisitionEntity = new RequisitionEntity(
                1L,
                requisitionNo,
                mmsCode,
                userId,
                new Timestamp(new Date().getTime()),
                amount,
                RequisitionStatus.SUBMITTED,
                "",
                "",
                "",
                "",
                new HashSet<>(List.of(requisitionItemEntity)));

        requisitionItemList.add(
                new RequisitionItemListDTO(
                        1L,
                        "Spring Bed",
                        "Furniture",
                        1,
                        "Unit",
                        ItemLifespan.ABOVE_1_YEAR.getItemLifespanValue(),
                        amount,
                        amount,
                        PaymentStatus.CREATED,
                        new HashSet<>(List.of(RandomStringUtils.randomAlphanumeric(5)))
                )
        );

        requisitionStatusAndListDTO = RequisitionStatusAndListDTO.builder()
                .list(requisitionList)
                .isRequisitionNotCompleted(true)
                .build();
    }

    @Test
    void getRequisitionList_ReturnSuccessTest() throws Exception {

        when(requisitionService.getRequisitions(anyString(),anyString(), anyBoolean(), anyBoolean(), anyString())).thenReturn(requisitionStatusAndListDTO);

        mockMvc.perform(MockMvcRequestBuilders
                        .get("/requisitions")
                        .header("UserId", userId)
                        .param("approval", "false")
                        .param("mmsCode", mmsCode)
                        .param("nik",userId)
                        .characterEncoding("utf-8"))
                .andDo(print())
                .andExpect(status().is2xxSuccessful())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value(ResponseMessage.REQUISITION_LIST_FETCHED))
                .andExpect(jsonPath("$.data.list[0].requestId").value(1L))
                .andExpect(jsonPath("$.data.list[0].createdByNik").value(userId))
                .andExpect(jsonPath("$.data.list[0].createdByName").value(userName));
    }

    @Test
    void getRequisitionListForApproval_ReturnSuccessTest() throws Exception {

        when(requisitionService.getRequisitions(anyString(),anyString(), anyBoolean(), anyBoolean(), anyString())).thenReturn(requisitionStatusAndListDTO);

        mockMvc.perform(MockMvcRequestBuilders
                        .get("/requisitions")
                        .header("UserId", userId)
                        .param("approval", "true")
                        .param("mmsCode", mmsCode)
                        .param("nik",userId)
                        .characterEncoding("utf-8"))
                .andDo(print())
                .andExpect(status().is2xxSuccessful())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value(ResponseMessage.REQUISITION_LIST_FETCHED))
                .andExpect(jsonPath("$.data.list[0].requestId").value(1L))
                .andExpect(jsonPath("$.data.list[0].createdByNik").value(userId))
                .andExpect(jsonPath("$.data.list[0].createdByName").value(userName));
    }

    @Test
    void getRequisitionList_ReturnGenericException() throws Exception {

        when(requisitionService.getRequisitions(anyString(),anyString(), anyBoolean(), anyBoolean(), anyString())).thenThrow(JDBCConnectionException.class);

        mockMvc.perform(MockMvcRequestBuilders
                        .get("/requisitions")
                        .header("UserId", userId)
                        .param("approval", "false")
                        .param("mmsCode", mmsCode)
                        .param("nik",userId)
                        .characterEncoding("utf-8"))
                .andDo(print())
                .andExpect(status().is5xxServerError())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").exists());
    }

    @Test
    void getRequisitionListForApproval_ReturnGenericException() throws Exception {

        when(requisitionService.getRequisitions(anyString(),anyString(), anyBoolean(), anyBoolean(), anyString())).thenThrow(JDBCConnectionException.class);

        mockMvc.perform(MockMvcRequestBuilders
                        .get("/requisitions")
                        .header("UserId", userId)
                        .param("approval", "true")
                        .param("mmsCode", mmsCode)
                        .param("nik",userId)
                        .characterEncoding("utf-8"))
                .andDo(print())
                .andExpect(status().is5xxServerError())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").exists());
    }

    @Test
    void submitRequisition_ReturnSuccessTest() throws Exception {

        when(requisitionService.submitRequisition(anyString(), anyString(), any())).thenReturn(requisitionNo);

        mockMvc.perform(MockMvcRequestBuilders
                        .post("/requisitions")
                        .header("UserId", userId)
                        .header("mmsCode", mmsCode)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(mapper.writeValueAsString(requisitionEntity))
                        .characterEncoding("utf-8"))
                .andDo(print())
                .andExpect(status().is2xxSuccessful())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value(requisitionNo));

    }

    @Test
    void submitRequisitionList_ReturnGenericException() throws Exception {

        when(requisitionService.submitRequisition(anyString(), anyString(), any())).thenThrow(JDBCConnectionException.class);

        mockMvc.perform(MockMvcRequestBuilders
                        .post("/requisitions")
                        .header("UserId", userId)
                        .header("mmsCode", mmsCode)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(mapper.writeValueAsString(requisitionEntity))
                        .characterEncoding("utf-8"))
                .andDo(print())
                .andExpect(status().is5xxServerError())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").exists());
    }

    @Test
    void submitApproval_ReturnSuccessTest() throws Exception {

        doNothing().when(requisitionService).submitApprovalRequisitions(any(), anyString(), anyString(), anyString(), anyString(),anyString());

        mockMvc.perform(MockMvcRequestBuilders
                        .post("/requisitions/approval")
                        .header("UserId", userId)
                        .header("CoCode", coCode)
                        .header("RetrievalReferenceNumber", rrn)
                        .header("Access-Token", accessToken)
                        .header("DeviceId",deviceId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(mapper.writeValueAsString(requisitionApprovalDTO))
                        .characterEncoding("utf-8"))
                .andDo(print())
                .andExpect(status().is2xxSuccessful())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value(ResponseMessage.APPROVAL_SUBMITTED));
    }

    @Test
    void submitApproval_ReturnStatusNotMatch() throws Exception {

        doThrow(new BusinessException(HttpStatus.BAD_REQUEST, ResponseMessage.STATUS_NOT_MATCH)).when(requisitionService).submitApprovalRequisitions(any(), anyString(), anyString(), anyString(), anyString(),anyString());

        mockMvc.perform(MockMvcRequestBuilders
                        .post("/requisitions/approval")
                        .header("UserId", userId)
                        .header("CoCode", coCode)
                        .header("RetrievalReferenceNumber", rrn)
                        .header("Access-Token", accessToken)
                        .header("DeviceId",deviceId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(mapper.writeValueAsString(requisitionApprovalDTO))
                        .characterEncoding("utf-8"))
                .andDo(print())
                .andExpect(status().is4xxClientError())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").exists());
    }

    @Test
    void submitApproval_ReturnGenericException() throws Exception {

        doThrow(JDBCConnectionException.class).when(requisitionService).submitApprovalRequisitions(any(), anyString(), anyString(), anyString(), anyString(),anyString());

        mockMvc.perform(MockMvcRequestBuilders
                        .post("/requisitions/approval")
                        .header("UserId", userId)
                        .header("CoCode", coCode)
                        .header("RetrievalReferenceNumber", rrn)
                        .header("Access-Token", accessToken)
                        .header("DeviceId",deviceId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(mapper.writeValueAsString(requisitionApprovalDTO))
                        .characterEncoding("utf-8"))
                .andDo(print())
                .andExpect(status().is5xxServerError())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").exists());
    }

    @Test
    void submitSettlement_ReturnSuccessTest() throws Exception {

        doNothing().when(requisitionService).submitRequisitionSettlement(anyLong(), anyString(), any());

        mockMvc.perform(MockMvcRequestBuilders
                        .post("/requisitions/settlement/1")
                        .header("UserId", userId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(mapper.writeValueAsString(cancelledItemIds))
                        .characterEncoding("utf-8"))
                .andDo(print())
                .andExpect(status().is2xxSuccessful())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value(ResponseMessage.REQUISITION_SETTLED));
    }

    @Test
    void submitSettlement_ReturnForbiddenAccess() throws Exception {

        doThrow(new BusinessException(HttpStatus.BAD_REQUEST, ResponseMessage.UNAUTHORIZED_ACCESS)).when(requisitionService).submitRequisitionSettlement(anyLong(), anyString(), any());

        mockMvc.perform(MockMvcRequestBuilders
                        .post("/requisitions/settlement/1")
                        .header("UserId", userId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(mapper.writeValueAsString(cancelledItemIds))
                        .characterEncoding("utf-8"))
                .andDo(print())
                .andExpect(status().is4xxClientError())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").exists());
    }

    @Test
    void submitSettlement_ReturnGenericException() throws Exception {

        doThrow(JDBCConnectionException.class).when(requisitionService).submitRequisitionSettlement(anyLong(), anyString(), any());

        mockMvc.perform(MockMvcRequestBuilders
                        .post("/requisitions/settlement/1")
                        .header("UserId", userId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(mapper.writeValueAsString(cancelledItemIds))
                        .characterEncoding("utf-8"))
                .andDo(print())
                .andExpect(status().is5xxServerError())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").exists());
    }
}
