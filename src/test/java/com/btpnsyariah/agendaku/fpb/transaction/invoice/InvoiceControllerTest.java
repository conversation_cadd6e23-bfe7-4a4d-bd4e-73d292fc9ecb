package com.btpnsyariah.agendaku.fpb.transaction.invoice;

import com.btpnsyariah.agendaku.fpb.model.ResponseMessage;
import com.btpnsyariah.agendaku.fpb.service.LoggingService;
import org.codehaus.jackson.map.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.web.client.HttpClientErrorException;

import java.util.UUID;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;


@WebMvcTest(InvoiceController.class)
@AutoConfigureMockMvc(addFilters = false)
@ExtendWith(MockitoExtension.class)
@ActiveProfiles("dev")
class InvoiceControllerTest {
    @Autowired
    private MockMvc mockMvc;

    @MockBean
    Logger logger;

    @MockBean
    private InvoiceServiceImpl invoiceServiceImpl;

    @MockBean
    private LoggingService loggingService;

    private final ObjectMapper mapper = new ObjectMapper();

    private final byte[] bytes = new byte[]{};

    private InvoiceUploadRequestDTO invoiceUploadRequestDTO;

    private ResponseEntity responseEntity;

    private MockMultipartFile requestBody;

    @BeforeEach
    public void setUp() {
        requestBody = new MockMultipartFile("invoice", "invoice.jpg".getBytes());
    }

    @Test
    void uploadInvoiceTest_ReturnSuccess() throws Exception {

        responseEntity = new ResponseEntity("Invoice uploaded successfully!", HttpStatus.OK);

        when(invoiceServiceImpl.uploadInvoice(anyLong(), anyLong(), anyString(), any())).thenReturn(responseEntity);

        this.mockMvc.perform(MockMvcRequestBuilders
                        .multipart("/invoices/upload/1/1")
                        .file(requestBody)
                        .header("Access-Token", UUID.randomUUID().toString())
                        .contentType(MediaType.MULTIPART_FORM_DATA)
                        .characterEncoding("utf-8"))
                .andDo(print())
                .andExpect(status().is2xxSuccessful());
    }

    @Test
    void uploadInvoiceTest_ThrowHttpClientErrorException() throws Exception {

        responseEntity = new ResponseEntity("Invoice uploaded successfully!", HttpStatus.OK);

        when(invoiceServiceImpl.uploadInvoice(anyLong(), anyLong(), anyString(), any())).thenThrow(new HttpClientErrorException(HttpStatus.BAD_REQUEST));

        this.mockMvc.perform(MockMvcRequestBuilders
                        .multipart("/invoices/upload/1/1")
                        .file(requestBody)
                        .header("Access-Token", UUID.randomUUID().toString())
                        .contentType(MediaType.MULTIPART_FORM_DATA)
                        .characterEncoding("utf-8"))
                .andDo(print())
                .andExpect(status().is4xxClientError())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value(String.format(ResponseMessage.SOMETHING_WENT_WRONG_ARG, "400 BAD_REQUEST")))
                .andExpect(jsonPath("$.code").exists());
    }

    @Test
    void uploadInvoiceTest_ThrowGenericException() throws Exception {

        responseEntity = new ResponseEntity("Invoice uploaded successfully!", HttpStatus.OK);

        when(invoiceServiceImpl.uploadInvoice(anyLong(), anyLong(), anyString(), any())).thenThrow(NullPointerException.class);

        this.mockMvc.perform(MockMvcRequestBuilders
                        .multipart("/invoices/upload/1/1")
                        .file(requestBody)
                        .header("Access-Token", UUID.randomUUID().toString())
                        .contentType(MediaType.MULTIPART_FORM_DATA)
                        .characterEncoding("utf-8"))
                .andDo(print())
                .andExpect(status().is5xxServerError())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value(ResponseMessage.SOMETHING_WENT_WRONG))
                .andExpect(jsonPath("$.code").exists());
    }

    @Test
    void getInvoiceTest_ReturnSuccess() throws Exception {

        responseEntity = new ResponseEntity(bytes,HttpStatus.OK);

        when(invoiceServiceImpl.getInvoice(anyString(), anyString())).thenReturn(responseEntity);

        this.mockMvc.perform(MockMvcRequestBuilders
                        .get("/invoices/" + UUID.randomUUID())
                        .header("Access-Token", UUID.randomUUID().toString())
                        .characterEncoding("utf-8"))
                .andDo(print())
                .andExpect(status().is2xxSuccessful())
                .andExpect(content().contentType(MediaType.APPLICATION_OCTET_STREAM));
    }

    @Test
    void getInvoiceTest_ThrowHttpClientErrorException() throws Exception {

        responseEntity = new ResponseEntity(bytes,HttpStatus.OK);

        when(invoiceServiceImpl.getInvoice(anyString(), anyString())).thenThrow(new HttpClientErrorException(HttpStatus.BAD_REQUEST));

        this.mockMvc.perform(MockMvcRequestBuilders
                        .get("/invoices/" + UUID.randomUUID())
                        .header("Access-Token", UUID.randomUUID().toString())
                        .characterEncoding("utf-8"))
                .andDo(print())
                .andExpect(status().is4xxClientError())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value(String.format(ResponseMessage.SOMETHING_WENT_WRONG_ARG,"400 BAD_REQUEST")))
                .andExpect(jsonPath("$.code").exists());
    }

    @Test
    void getInvoiceTest_ThrowGenericException() throws Exception {

        responseEntity = new ResponseEntity(bytes,HttpStatus.OK);

        when(invoiceServiceImpl.getInvoice(anyString(), anyString())).thenThrow(NullPointerException.class);

        this.mockMvc.perform(MockMvcRequestBuilders
                        .get("/invoices/" + UUID.randomUUID())
                        .header("Access-Token", UUID.randomUUID().toString())
                        .characterEncoding("utf-8"))
                .andDo(print())
                .andExpect(status().is5xxServerError())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value(ResponseMessage.SOMETHING_WENT_WRONG))
                .andExpect(jsonPath("$.code").exists());
    }
}
