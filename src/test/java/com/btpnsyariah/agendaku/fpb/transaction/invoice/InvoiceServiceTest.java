package com.btpnsyariah.agendaku.fpb.transaction.invoice;

import com.btpnsyariah.agendaku.fpb.exception.BusinessException;
import com.btpnsyariah.agendaku.fpb.model.BaseResponse;
import com.btpnsyariah.agendaku.fpb.model.ResponseMessage;
import com.btpnsyariah.agendaku.fpb.service.LoggingService;
import com.btpnsyariah.agendaku.fpb.service.StorageServiceImpl;
import com.btpnsyariah.agendaku.fpb.transaction.items.ItemService;
import com.btpnsyariah.agendaku.fpb.transaction.items.PaymentStatus;
import com.btpnsyariah.agendaku.fpb.transaction.items.RequisitionItemEntity;
import com.btpnsyariah.agendaku.fpb.transaction.items.RequisitionItemListDTO;
import com.btpnsyariah.agendaku.fpb.transaction.requisition.RequisitionEntity;
import com.btpnsyariah.agendaku.fpb.transaction.requisition.RequisitionService;
import com.btpnsyariah.agendaku.fpb.transaction.requisition.RequisitionStatus;
import org.apache.commons.lang3.RandomStringUtils;
import org.codehaus.jackson.map.ObjectMapper;
import org.junit.Ignore;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@ActiveProfiles("dev")
class InvoiceServiceTest {

    @Autowired
    private MockMvc mockMvc;

    @Mock
    RestTemplate restTemplate;

    @InjectMocks
    private InvoiceServiceImpl invoiceServiceImpl;

    @Mock
    private RequisitionService requisitionService;

    @Mock
    private ItemService itemService;

    @Mock
    private StorageServiceImpl storageServiceImpl;

    @Mock
    private LoggingService loggingService;

    @Mock
    private InvoiceRepository invoiceRepository;

    private InvoiceUploadRequestDTO invoiceUploadRequestDTO;

    private final ObjectMapper mapper = new ObjectMapper();

    private final byte[] bytes = new byte[]{};

    private BaseResponse baseResponse;

    private ResponseEntity responseOk;

    private ResponseEntity responseBadRequest;

    private MockMultipartFile file;

    private final String mmsCode = "W0477";

    private final String userId = "MIT012";

    private final String note = "note";

    private final String userName = "Isma Hariani";

    private final Timestamp timestamp = new Timestamp(new Date().getTime());

    private final BigDecimal amount = new BigDecimal("1221250.0000");

    private final String requisitionNo = "3/W0477/4/2023";

    private final String requisitionNoInRoman = "0003/W0477/IV/2023";

    private RequisitionEntity requisitionEntity = new RequisitionEntity();

    private RequisitionItemEntity requisitionItemEntity = new RequisitionItemEntity();

    private InvoiceEntity invoiceEntity = new InvoiceEntity();

    private final List<RequisitionItemListDTO> requisitionItemList = new ArrayList<>();


    @BeforeEach
    public void setUp() {

        baseResponse = new BaseResponse();

        baseResponse = new BaseResponse(false, "Failed to get invoice : N/A",null,null);

        BaseResponse.builder().code(null).message("Success").success(true).data(null);

        responseOk = new ResponseEntity(HttpStatus.OK);
        responseBadRequest = new ResponseEntity(HttpStatus.BAD_REQUEST);

        requisitionItemEntity = new RequisitionItemEntity(
                1L,
                requisitionEntity,
                1L,
                1,
                timestamp,
                PaymentStatus.CREATED,
                amount,
                amount,
                null,
                null
        );

        requisitionEntity = new RequisitionEntity(
                1L,
                requisitionNoInRoman,
                mmsCode,
                userId,
                timestamp,
                amount,
                RequisitionStatus.SUBMITTED,
                "",
                "",
                "",
                "",
                new HashSet<>(List.of(requisitionItemEntity)));

        invoiceEntity = new InvoiceEntity(
                1L,
                RandomStringUtils.randomAlphanumeric(5),
                false
        );

    }

    @Test
    void uploadInvoiceServiceTest_ReturnSuccess() throws IOException, BusinessException {

        when(storageServiceImpl.send(
                any(),
                anyString(),
                anyString(),
                any(HttpMethod.class),
                any(MediaType.class))).thenReturn(responseOk);

        when(requisitionService.findRequisitionById(anyLong())).thenReturn(requisitionEntity);
        when(itemService.findItemByRequisitionIdAndItemId(anyLong(), anyLong())).thenReturn(requisitionItemEntity);
        doNothing().when(itemService).checkAndUpdatePaymentStatusByInvoiceId(any());

        ResponseEntity testResponse = invoiceServiceImpl.uploadInvoice(
                1L,
                1L,
                UUID.randomUUID().toString(),
                file);

        assertThat(testResponse).isEqualTo(new ResponseEntity(new BaseResponse(true, ResponseMessage.INVOICE_UPLOADED), HttpStatus.OK));
    }

    @Test
    void uploadInvoiceServiceTest_ReturnFailed() throws IOException, BusinessException {

        when(storageServiceImpl.send(
                any(),
                anyString(),
                anyString(),
                any(HttpMethod.class),
                any(MediaType.class))).thenReturn(responseBadRequest);

        when(requisitionService.findRequisitionById(anyLong())).thenReturn(requisitionEntity);

        ResponseEntity testResponse = invoiceServiceImpl.uploadInvoice(
                1L,
                1L,
                UUID.randomUUID().toString(),
                file);

        assertThat(testResponse).isEqualTo(new ResponseEntity(new BaseResponse(false, String.format(ResponseMessage.INVOICE_UPLOAD_FAIL,"N/A")), HttpStatus.BAD_REQUEST));
    }

    @Test
    void getInvoiceServiceTest_ReturnSuccess() {

        ResponseEntity response = new ResponseEntity(bytes,HttpStatus.OK);

        when(storageServiceImpl.send(
                anyString(),
                anyString(),
                any(HttpMethod.class))).thenReturn(response);

        ResponseEntity testResponse = invoiceServiceImpl.getInvoice(UUID.randomUUID().toString(), UUID.randomUUID().toString());

        assertThat(testResponse).isEqualTo(response);
    }

    @Test
    void getInvoiceServiceTest_ReturnFailed() {

        when(storageServiceImpl.send(
                anyString(),
                anyString(),
                any(HttpMethod.class))).thenReturn(responseBadRequest);

        ResponseEntity testResponse = invoiceServiceImpl.getInvoice(UUID.randomUUID().toString(), UUID.randomUUID().toString());

        assertThat(testResponse).isEqualTo(new ResponseEntity(new BaseResponse(false, String.format(ResponseMessage.INVOICE_FETCH_FAIL,"N/A")), HttpStatus.BAD_REQUEST));
    }
}
