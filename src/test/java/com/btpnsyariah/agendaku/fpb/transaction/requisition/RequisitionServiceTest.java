package com.btpnsyariah.agendaku.fpb.transaction.requisition;

import com.btpnsyariah.agendaku.fpb.catalogue.ItemLifespan;
import com.btpnsyariah.agendaku.fpb.document.DocumentService;
import com.btpnsyariah.agendaku.fpb.exception.BusinessException;
import com.btpnsyariah.agendaku.fpb.model.CashUserPersonasEntity;
import com.btpnsyariah.agendaku.fpb.model.PersonaRole;
import com.btpnsyariah.agendaku.fpb.model.ResponseMessage;
import com.btpnsyariah.agendaku.fpb.other.service.NationalHolidayService;
import com.btpnsyariah.agendaku.fpb.service.NotificationConfigurator;
import com.btpnsyariah.agendaku.fpb.transaction.items.*;
import com.btpnsyariah.agendaku.fpb.transaction.log.TransactionLogService;
import com.btpnsyariah.agendaku.fpb.user.CashUserPersonaService;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.assertAll;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@ActiveProfiles("dev")
class RequisitionServiceTest {

    @Autowired
    private MockMvc mockMvc;

    @InjectMocks
    private RequisitionServiceImpl requisitionService;

    @Mock
    private RequisitionRepository requisitionRepository;

    @Mock
    private TransactionLogService transactionLogService;

    @Mock
    private NotificationConfigurator notificationConfigurator;

    @Mock
    private CashUserPersonaService cashUserPersonaService;

    @Mock
    private DocumentService documentService;

    @Mock
    private ItemService itemService;

    @Mock
    private NationalHolidayService nationalHolidayService;

    private final String mmsCode = "W0477";

    private final String userId = "MIT012";

    private final String note = "note";

    private final String userName = "Isma Hariani";

    private final Timestamp timestamp = new Timestamp(new Date().getTime());

    private final BigDecimal amount = new BigDecimal("1221250.0000");

    private final String requisitionNo = "3/W0477/4/2023";

    private final String invoiceId = "15084723835nBU-452";

    private final String requisitionNoInRoman = "0003/W0477/IV/2023";

    private RequisitionStatusAndListDTO requisitionStatusAndListDTO = new RequisitionStatusAndListDTO();
    private final List<RequisitionListDTO> requisitionList = new ArrayList<>();

    private final List<RequisitionListProjection> requisitionListProjections = new ArrayList<>();

    private final List<RequisitionListProjection> requisitionListProjectionsForSubmit = new ArrayList<>();

    private final List<RequisitionItemListProjection> requisitionItemListProjections = new ArrayList<>();

    private RequisitionEntity requisitionEntity = new RequisitionEntity();

    private CashUserPersonasEntity cashUserPersonasEntity = new CashUserPersonasEntity();

    private final List<RequisitionItemListDTO> requisitionItemList = new ArrayList<>();

    private final RequisitionApprovalDTO requisitionApprovalDTO = new RequisitionApprovalDTO(
            1L,
            RequisitionStatus.REQUEST_APPROVED_BM,
            ""
    );

    private final long[] cancelledItemIds = {1,2,3};

    private final String coCode = RandomStringUtils.randomAlphanumeric(4);

    private final String accessToken = RandomStringUtils.randomAlphanumeric(15);

    private final String rrn = RandomStringUtils.randomAlphanumeric(10);

    private final String deviceId = RandomStringUtils.randomAlphanumeric(5);

    @BeforeEach
    public void setUp() {

        RequisitionListProjection requisitionListProjection = new RequisitionListProjection() {
            @Override
            public Long getId() {
                return 1L;
            }

            @Override
            public String getRefNo() {
                return requisitionNoInRoman;
            }

            @Override
            public Timestamp getCreatedDate() {
                return timestamp;
            }

            @Override
            public Timestamp getSettlementDate() {
                return timestamp;
            }

            @Override
            public String getCreatedByNik() {
                return userId;
            }

            @Override
            public String getCreatedByName() {
                return userName;
            }

            @Override
            public BigDecimal getRequestAmount() {
                return amount;
            }

            @Override
            public BigDecimal getActualizedAmount(){
                return amount;
            }

            @Override
            public RequisitionStatus getRequestStatus() {
                return RequisitionStatus.SUBMITTED;
            }

            @Override
            public String getNote() {
                return note;
            }

            @Override
            public RequisitionStatus getPreviousStatus() {
                return RequisitionStatus.SUBMITTED;
            }

            @Override
            public Timestamp getFundDisbursedDate() {
                return new Timestamp(new Date().getTime());
            };

            @Override
            public boolean getBaExist() {
                return false;
            };
        };

        RequisitionListProjection requisitionListProjectionForSubmit = new RequisitionListProjection() {
            @Override
            public Long getId() {
                return 1L;
            }

            @Override
            public String getRefNo() {
                return requisitionNo;
            }

            @Override
            public Timestamp getCreatedDate() {
                return timestamp;
            }

            @Override
            public Timestamp getSettlementDate() {
                return timestamp;
            }

            @Override
            public String getCreatedByNik() {
                return userId;
            }

            @Override
            public String getCreatedByName() {
                return userName;
            }

            @Override
            public BigDecimal getRequestAmount() {
                return amount;
            }

            @Override
            public BigDecimal getActualizedAmount(){
                return amount;
            }

            @Override
            public RequisitionStatus getRequestStatus() {
                return RequisitionStatus.SUBMITTED;
            }

            @Override
            public String getNote() {
                return note;
            }

            @Override
            public RequisitionStatus getPreviousStatus() {
                return RequisitionStatus.SUBMITTED;
            }

            @Override
            public Timestamp getFundDisbursedDate() {
                return new Timestamp(new Date().getTime());
            };

            @Override
            public boolean getBaExist() {
                return false;
            };
        };

        RequisitionItemListProjection requisitionItemListProjection =  new RequisitionItemListProjection() {
            @Override
            public Long getId() {
                return 1L;
            }
            @Override
            public Long getItemId() {
                return 1L;
            }
            @Override
            public String getItemName() {
                return "Spring Bed";
            }

            @Override
            public String getItemCategory() {
                return "Furniture";
            }

            @Override
            public int getItemQty() {
                return 1;
            }

            @Override
            public String getMeasurementUnit() {
                return "Unit";
            }

            @Override
            public ItemLifespan getItemLifeSpan() {
                return ItemLifespan.ABOVE_1_YEAR;
            }

            @Override
            public BigDecimal getPrice() {
                return amount;
            }

            @Override
            public BigDecimal getActualAmount() {
                return amount;
            }

            @Override
            public PaymentStatus getPaymentStatus() {
                return PaymentStatus.CREATED;
            }
        };

        requisitionListProjections.add(requisitionListProjection);
        requisitionListProjectionsForSubmit.add(requisitionListProjectionForSubmit);
        requisitionItemListProjections.add(requisitionItemListProjection);

        requisitionList.add(
                new RequisitionListDTO(
                        1L,
                        requisitionNo,
                        timestamp,
                        timestamp,
                        userId,
                        userName,
                        amount,
                        amount,
                        RequisitionStatus.SUBMITTED,
                        note
                )
        );

        requisitionEntity = new RequisitionEntity();

        RequisitionItemEntity requisitionItemEntity = new RequisitionItemEntity();

        requisitionItemEntity = new RequisitionItemEntity(
                1L,
                requisitionEntity,
                1L,
                1,
                timestamp,
                PaymentStatus.CREATED,
                amount,
                amount,
                null,
                null
        );

        requisitionEntity = new RequisitionEntity(
                1L,
                requisitionNoInRoman,
                mmsCode,
                userId,
                timestamp,
                amount,
                RequisitionStatus.SUBMITTED,
                "",
                "",
                "",
                "",
                new HashSet<>(List.of(requisitionItemEntity)));

        requisitionItemList.add(
                new RequisitionItemListDTO(
                        1L,
                        "Spring Bed",
                        "Furniture",
                        1,
                        "Unit",
                        ItemLifespan.ABOVE_1_YEAR.getItemLifespanValue(),
                        amount,
                        amount,
                        PaymentStatus.CREATED,
                        new HashSet<>(List.of(RandomStringUtils.randomAlphanumeric(5)))
                )
        );

        requisitionStatusAndListDTO = RequisitionStatusAndListDTO.builder()
                .list(requisitionList)
                .isRequisitionNotCompleted(true)
                .build();

        cashUserPersonasEntity = new CashUserPersonasEntity(
                "W0477",
                "MMS MAUK",
                "MIT012",
                "MIT012",
                "ISMA HARIANI",
                "",
                PersonaRole.KW.getPersonaRole()
        );
    }

    @Test
    void testGetRequisitions() throws BusinessException {

        when(requisitionRepository.findByNik(userId)).thenReturn(requisitionListProjections);

        RequisitionStatusAndListDTO  response = requisitionService.getRequisitions(userId,userId, false, false, mmsCode);

        assertThat(response.getList().get(0)).isNotNull();
        assertThat(response.getList().get(0).getRequestId()).isSameAs(requisitionListProjections.get(0).getId());
        assertThat(response.getList().get(0).getRefNo()).isEqualToIgnoringCase(requisitionNoInRoman);
        assertThat(response.getList().get(0).getCreatedDate()).isSameAs(requisitionListProjections.get(0).getCreatedDate());
        assertThat(response.getList().get(0).getCreatedByNik()).isSameAs(requisitionListProjections.get(0).getCreatedByNik());
        assertThat(response.getList().get(0).getCreatedByName()).isEqualTo(requisitionListProjections.get(0).getCreatedByName());
        assertThat(response.getList().get(0).getRequestAmount()).isSameAs(requisitionListProjections.get(0).getRequestAmount());
        assertThat(response.getList().get(0).getRequestStatus()).isSameAs(requisitionListProjections.get(0).getRequestStatus());

    }

    @Test
    void testGetRequisitionsForApproval() throws BusinessException {

        when(requisitionRepository.findByMmsCodeForList(mmsCode)).thenReturn(requisitionListProjections);

        RequisitionStatusAndListDTO response = requisitionService.getRequisitions(userId,userId, true, false, mmsCode);

        assertThat(response.getList().get(0)).isNotNull();
        assertThat(response.getList().get(0).getRequestId()).isSameAs(requisitionListProjections.get(0).getId());
        assertThat(response.getList().get(0).getRefNo()).isEqualToIgnoringCase(requisitionNoInRoman);
        assertThat(response.getList().get(0).getCreatedDate()).isSameAs(requisitionListProjections.get(0).getCreatedDate());
        assertThat(response.getList().get(0).getCreatedByNik()).isSameAs(requisitionListProjections.get(0).getCreatedByNik());
        assertThat(response.getList().get(0).getCreatedByName()).isEqualTo(requisitionListProjections.get(0).getCreatedByName());
        assertThat(response.getList().get(0).getRequestAmount()).isSameAs(requisitionListProjections.get(0).getRequestAmount());
        assertThat(response.getList().get(0).getRequestStatus()).isSameAs(requisitionListProjections.get(0).getRequestStatus());

    }

    @Test
    void testSubmitRequisition() throws BusinessException {
        when(requisitionRepository.save(requisitionEntity)).thenReturn(requisitionEntity);
        when(requisitionRepository.findByMmsCode(mmsCode)).thenReturn(requisitionListProjectionsForSubmit);
        String response = requisitionService.submitRequisition(userId, mmsCode, requisitionEntity);
        assertThat(response).isNotNull().isNotBlank().isEqualToIgnoringCase(requisitionNoInRoman);

    }

    @Test
    void testSubmitApprovalApprovedBM() {

        when(requisitionRepository.findById(1L)).thenReturn(Optional.of(requisitionEntity));
        assertAll(() -> requisitionService.submitApprovalRequisitions(requisitionApprovalDTO, userId, accessToken, coCode, rrn,deviceId));

    }

/*    @Test
    void testSubmitApprovalApprovedKW() throws BusinessException {

        when(cashUserPersonaService.findByNik(anyString())).thenReturn(cashUserPersonasEntity);
        requisitionApprovalDTO.setRequestStatus(RequisitionStatus.REQUEST_APPROVED_PIC_KAS);
        requisitionEntity.setRequestStatus(RequisitionStatus.REQUEST_APPROVED_BM);
        when(requisitionRepository.findById(1L)).thenReturn(Optional.of(requisitionEntity));
        assertAll(() -> requisitionService.submitApprovalRequisitions(requisitionApprovalDTO, userId));

    }*/

    @Test
    void testSubmitApprovalRejectedBM() {

        requisitionApprovalDTO.setRequestStatus(RequisitionStatus.REQUEST_REJECTED_BM);
        requisitionEntity.setRequestStatus(RequisitionStatus.SUBMITTED);
        when(requisitionRepository.findById(1L)).thenReturn(Optional.of(requisitionEntity));
        assertAll(() -> requisitionService.submitApprovalRequisitions(requisitionApprovalDTO, userId, accessToken, coCode, rrn,deviceId));

    }

    @Test
    void testSubmitApprovalRejectedKW() {

        requisitionApprovalDTO.setRequestStatus(RequisitionStatus.REQUEST_REJECTED_PIC_KAS);
        requisitionEntity.setRequestStatus(RequisitionStatus.REQUEST_APPROVED_BM);
        when(requisitionRepository.findById(1L)).thenReturn(Optional.of(requisitionEntity));
        assertAll(() -> requisitionService.submitApprovalRequisitions(requisitionApprovalDTO, userId, accessToken, coCode, rrn,deviceId));

    }

    @Test
    void testSubmitApproval_ThrowsInvalidParam() {

        requisitionApprovalDTO.setRequestStatus(RequisitionStatus.IN_PROGRESS);
        when(requisitionRepository.findById(1L)).thenReturn(Optional.of(requisitionEntity));
        assertThatThrownBy(() -> requisitionService.submitApprovalRequisitions(requisitionApprovalDTO, userId, accessToken, coCode, rrn,deviceId))
                .isExactlyInstanceOf(BusinessException.class)
                .hasMessage(ResponseMessage.INVALID_PARAM);

    }

    @Test
    void testSubmitApproval_ThrowsNotFound() {

        when(requisitionRepository.findById(1L)).thenReturn(Optional.empty());
        assertThatThrownBy(() -> requisitionService.submitApprovalRequisitions(requisitionApprovalDTO, userId, accessToken, coCode, rrn,deviceId))
                .isExactlyInstanceOf(BusinessException.class)
                .hasMessage(ResponseMessage.REQUISITION_NOT_FOUND);

    }

    @Test
    void testSubmitApproval_ThrowsStatusNotMatch() {

        requisitionEntity.setRequestStatus(RequisitionStatus.CANCELLED);
        when(requisitionRepository.findById(anyLong())).thenReturn(Optional.of(requisitionEntity));
        assertThatThrownBy(() -> requisitionService.submitApprovalRequisitions(requisitionApprovalDTO, userId, accessToken, coCode, rrn,deviceId))
                .isExactlyInstanceOf(BusinessException.class)
                .hasMessage(ResponseMessage.STATUS_NOT_MATCH);

    }

    @Test
    void testValidateRequisition_ReturnSuccess() {

        when(requisitionRepository.findById(anyLong())).thenReturn(Optional.of(requisitionEntity));
        assertAll(() -> requisitionService.validateRequisition(1L));

    }

    @Test
    void testValidateRequisition_ThrowsNotFound() {

        when(requisitionRepository.findById(anyLong())).thenReturn(Optional.empty());
        assertThatThrownBy(() -> requisitionService.validateRequisition(1L))
                .isExactlyInstanceOf(BusinessException.class)
                .hasMessage(ResponseMessage.REQUISITION_NOT_FOUND);

    }

    @Test
    void testFindRequisitionById_ReturnSuccess() throws BusinessException {

        when(requisitionRepository.findById(anyLong())).thenReturn(Optional.of(requisitionEntity));
        RequisitionEntity response = requisitionService.findRequisitionById(1L);
        assertThat(response).isEqualTo(requisitionEntity);

    }

    @Test
    void testFindRequisitionById_ThrowsNotFound() {

        when(requisitionRepository.findById(anyLong())).thenReturn(Optional.empty());
        assertThatThrownBy(() -> requisitionService.findRequisitionById(1L))
                .isExactlyInstanceOf(BusinessException.class)
                .hasMessage(ResponseMessage.REQUISITION_NOT_FOUND);

    }

/*    @Test
    void testSubmitRequisitionSettlement_ReturnSuccess() {

        requisitionEntity.setRequestStatus(RequisitionStatus.IN_PROGRESS);
        requisitionEntity.setRequisitionItems(requisitionEntity.getRequisitionItems()
                .stream()
                .peek(i -> {
                    i.setPaymentStatus(PaymentStatus.ACTUALIZED);
                    i.setActualAmount(amount);
                    i.setInvoiceId(RandomStringUtils.randomAlphanumeric(5));
                })
                .collect(Collectors.toSet()));
        when(requisitionRepository.findById(anyLong())).thenReturn(Optional.of(requisitionEntity));
        assertAll(() -> requisitionService.submitRequisitionSettlement(1L, userId));

    }*/

    @Test
    void testSubmitRequisitionSettlement_ThrowsStatusNotMatch() {

        requisitionEntity.setRequestStatus(RequisitionStatus.IN_PROGRESS);
        requisitionEntity.setRequisitionItems(requisitionEntity.getRequisitionItems()
                .stream()
                .peek(i -> {
                    i.setPaymentStatus(PaymentStatus.UTILIZED);
                    i.setActualAmount(amount);
                })
                .collect(Collectors.toSet()));
        when(requisitionRepository.findById(anyLong())).thenReturn(Optional.of(requisitionEntity));
        when(cashUserPersonaService.validateUserPrivileges(anyString(), anyString())).thenReturn(true);
        when(itemService.filterCancelledItems(any(), any())).thenReturn(requisitionEntity);
        assertThatThrownBy(() -> requisitionService.submitRequisitionSettlement(1L, userId, cancelledItemIds))
                .isExactlyInstanceOf(BusinessException.class)
                .hasMessage(ResponseMessage.STATUS_NOT_MATCH);

    }

    @Test
    void testSubmitRequisitionSettlement_ThrowsStatusNotMatchCancelled() {

        requisitionEntity.setRequestStatus(RequisitionStatus.IN_PROGRESS);
        requisitionEntity.setRequisitionItems(requisitionEntity.getRequisitionItems()
                .stream()
                .peek(i -> {
                    i.setPaymentStatus(PaymentStatus.CANCELLED);
                })
                .collect(Collectors.toSet()));
        when(requisitionRepository.findById(anyLong())).thenReturn(Optional.of(requisitionEntity));
        when(cashUserPersonaService.validateUserPrivileges(anyString(), anyString())).thenReturn(true);
        when(itemService.filterCancelledItems(any(), any())).thenReturn(requisitionEntity);
        assertThatThrownBy(() -> requisitionService.submitRequisitionSettlement(1L, userId, cancelledItemIds))
                .isExactlyInstanceOf(BusinessException.class)
                .hasMessage(ResponseMessage.STATUS_NOT_MATCH);

    }

    @Test
    void testSubmitRequisitionSettlement_ThrowsForbidden() {

        requisitionEntity.setRequestStatus(RequisitionStatus.IN_PROGRESS);
        when(requisitionRepository.findById(anyLong())).thenReturn(Optional.of(requisitionEntity));
        when(itemService.filterCancelledItems(any(), any())).thenReturn(requisitionEntity);
        assertThatThrownBy(() -> requisitionService.submitRequisitionSettlement(1L, "22141938", cancelledItemIds))
                .isExactlyInstanceOf(BusinessException.class)
                .hasMessage(ResponseMessage.UNAUTHORIZED_ACCESS);

    }


}
