package com.btpnsyariah.agendaku.fpb.transaction.log;


import com.btpnsyariah.agendaku.fpb.exception.BusinessException;
import com.btpnsyariah.agendaku.fpb.model.CashUserPersonasEntity;
import com.btpnsyariah.agendaku.fpb.model.PersonaRole;
import com.btpnsyariah.agendaku.fpb.transaction.items.PaymentStatus;
import com.btpnsyariah.agendaku.fpb.transaction.items.RequisitionItemEntity;
import com.btpnsyariah.agendaku.fpb.transaction.items.RequisitionItemListDTO;
import com.btpnsyariah.agendaku.fpb.transaction.requisition.RequisitionEntity;
import com.btpnsyariah.agendaku.fpb.transaction.requisition.RequisitionStatus;
import com.btpnsyariah.agendaku.fpb.user.CashUserPersonaService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertAll;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@ActiveProfiles("dev")
class LogServiceTest {

    @Autowired
    private MockMvc mockMvc;

    @InjectMocks
    private TransactionLogServiceImpl transactionLogService;

    @Mock
    private TransactionLogRepository transactionLogRepository;

    @Mock
    private CashUserPersonaService cashUserPersonaService;

    private final String mmsCode = "W0477";

    private final String userId = "MIT012";

    private final Timestamp timestamp = new Timestamp(new Date().getTime());

    private final BigDecimal amount = new BigDecimal("1221250.0000");
    private final String requisitionNoInRoman = "0003/W0477/IV/2023";

    private final String note = "Test";

    private RequisitionEntity requisitionEntity = new RequisitionEntity();

    private final List<RequisitionItemListDTO> requisitionItemList = new ArrayList<>();

    private CashUserPersonasEntity cashUserPersonasEntity = new CashUserPersonasEntity();

    @BeforeEach
    public void setUp() {

        requisitionEntity = new RequisitionEntity();

        RequisitionItemEntity requisitionItemEntity = new RequisitionItemEntity();

        requisitionItemEntity = new RequisitionItemEntity(
                1L,
                requisitionEntity,
                1L,
                1,
                timestamp,
                PaymentStatus.CREATED,
                amount,
                amount,
                null,
                null
        );

        requisitionEntity = new RequisitionEntity(
                1L,
                requisitionNoInRoman,
                mmsCode,
                userId,
                timestamp,
                amount,
                RequisitionStatus.SUBMITTED,
                "",
                "",
                "",
                "",
                new HashSet<>(List.of(requisitionItemEntity)));

        cashUserPersonasEntity = new CashUserPersonasEntity(
                "W0477",
                "MMS MAUK",
                "MIT012",
                "MIT012",
                "ISMA HARIANI",
                "",
                PersonaRole.KW.getPersonaRole()
        );

    }

    @Test
    void testLogTransaction() throws BusinessException {
        when(cashUserPersonaService.findByNik(anyString())).thenReturn(cashUserPersonasEntity);
        assertAll(() -> transactionLogService.logRequisition(requisitionEntity.getId(), requisitionEntity.getRequestStatus(), userId, note));
    }

}
