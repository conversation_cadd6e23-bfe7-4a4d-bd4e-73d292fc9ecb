package com.btpnsyariah.agendaku.fpb.transaction.item;

import com.btpnsyariah.agendaku.fpb.catalogue.CatalogueEntity;
import com.btpnsyariah.agendaku.fpb.catalogue.CatalogueService;
import com.btpnsyariah.agendaku.fpb.catalogue.ItemLifespan;
import com.btpnsyariah.agendaku.fpb.exception.BusinessException;
import com.btpnsyariah.agendaku.fpb.model.ResponseMessage;
import com.btpnsyariah.agendaku.fpb.service.NotificationConfigurator;
import com.btpnsyariah.agendaku.fpb.transaction.invoice.InvoiceService;
import com.btpnsyariah.agendaku.fpb.transaction.items.*;
import com.btpnsyariah.agendaku.fpb.transaction.log.TransactionLogService;
import com.btpnsyariah.agendaku.fpb.transaction.requisition.*;
import com.btpnsyariah.agendaku.fpb.user.CashUserPersonaService;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.assertAll;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@ActiveProfiles("dev")
class ItemServiceTest {

    @Autowired
    private MockMvc mockMvc;

    @InjectMocks
    private ItemServiceImpl itemService;

    @Mock
    private RequisitionService requisitionService;

    @Mock
    private ItemRepository itemRepository;

    @Mock
    private RequisitionRepository requisitionRepository;

    @Mock
    private TransactionLogService transactionLogService;

    @Mock
    private NotificationConfigurator notificationConfigurator;

    @Mock
    private CashUserPersonaService cashUserPersonaService;

    @Mock
    private CatalogueService catalogueService;

    @Mock
    private InvoiceService invoiceService;

    private final String mmsCode = "W0477";

    private final String userId = "MIT012";

    private final String note = "note";

    private final String userName = "Isma Hariani";

    private final Timestamp timestamp = new Timestamp(new Date().getTime());

    private final BigDecimal amount = new BigDecimal("1221250.0000");

    private final String requisitionNo = "3/W0477/4/2023";

    private final String invoiceId = "15084723835nBU-452";

    private final String requisitionNoInRoman = "0003/W0477/IV/2023";
    private final String catalogItemName = "Helm";

    private final Set<String> invoices = new HashSet<>(List.of(RandomStringUtils.randomAlphanumeric(5)));

    private final List<RequisitionListDTO> requisitionList = new ArrayList<>();

    private final List<RequisitionListProjection> requisitionListProjections = new ArrayList<>();

    private final List<RequisitionItemListProjection> requisitionItemListProjections = new ArrayList<>();

    private RequisitionEntity requisitionEntity = new RequisitionEntity();

    private CatalogueEntity catalogueEntity = new CatalogueEntity();
    private final List<RequisitionItemListDTO> requisitionItemList = new ArrayList<>();

    private final RequisitionItemEntity requisitionItemEntity = new RequisitionItemEntity();

    private final RequisitionApprovalDTO requisitionApprovalDTO = new RequisitionApprovalDTO(
            1L,
            RequisitionStatus.REQUEST_APPROVED_BM,
            ""
    );

    @BeforeEach
    public void setUp() {

        RequisitionListProjection requisitionListProjection = new RequisitionListProjection() {
            @Override
            public Long getId() {
                return 1L;
            }

            @Override
            public String getRefNo() {
                return requisitionNo;
            }

            @Override
            public Timestamp getCreatedDate() {
                return timestamp;
            }

            @Override
            public Timestamp getSettlementDate() {
                return timestamp;
            }

            @Override
            public String getCreatedByNik() {
                return userId;
            }

            @Override
            public String getCreatedByName() {
                return userName;
            }

            @Override
            public BigDecimal getRequestAmount() {
                return amount;
            }

            @Override
            public BigDecimal getActualizedAmount(){
                return amount;
            }

            @Override
            public RequisitionStatus getRequestStatus() {
                return RequisitionStatus.SUBMITTED;
            }

            @Override
            public String getNote() {
                return note;
            }

            @Override
            public RequisitionStatus getPreviousStatus() {
                return RequisitionStatus.SUBMITTED;
            }

            @Override
            public Timestamp getFundDisbursedDate() {
                return new Timestamp(new Date().getTime());
            };

            @Override
            public boolean getBaExist() {
                return false;
            };
        };

        RequisitionItemListProjection requisitionItemListProjection =  new RequisitionItemListProjection() {
            @Override
            public Long getId(){
                return 1L;
            }
            @Override
            public Long getItemId() {
                return 1L;
            }
            @Override
            public String getItemName() {
                return "Spring Bed";
            }

            @Override
            public String getItemCategory() {
                return "Furniture";
            }

            @Override
            public int getItemQty() {
                return 1;
            }

            @Override
            public String getMeasurementUnit() {
                return "Unit";
            }

            @Override
            public ItemLifespan getItemLifeSpan() {
                return ItemLifespan.ABOVE_1_YEAR;
            }

            @Override
            public BigDecimal getPrice() {
                return amount;
            }

            @Override
            public BigDecimal getActualAmount() {
                return amount;
            }

            @Override
            public PaymentStatus getPaymentStatus() {
                return PaymentStatus.CREATED;
            }
        };

        requisitionListProjections.add(requisitionListProjection);
        requisitionItemListProjections.add(requisitionItemListProjection);

        requisitionList.add(
                new RequisitionListDTO(
                        1L,
                        requisitionNo,
                        timestamp,
                        timestamp,
                        userId,
                        userName,
                        amount,
                        amount,
                        RequisitionStatus.SUBMITTED,
                        note
                )
        );

        requisitionEntity = new RequisitionEntity();

        RequisitionItemEntity requisitionItemEntity = new RequisitionItemEntity();

        requisitionItemEntity = new RequisitionItemEntity(
                1L,
                requisitionEntity,
                1L,
                1,
                timestamp,
                PaymentStatus.CREATED,
                amount,
                amount,
                null,
                null
        );

        requisitionEntity = new RequisitionEntity(
                1L,
                requisitionNoInRoman,
                mmsCode,
                userId,
                timestamp,
                amount,
                RequisitionStatus.SUBMITTED,
                "",
                "",
                "",
                "",
                new HashSet<>(List.of(requisitionItemEntity)));

        requisitionItemList.add(
                new RequisitionItemListDTO(
                        1L,
                        "Spring Bed",
                        "Furniture",
                        1,
                        "Unit",
                        ItemLifespan.ABOVE_1_YEAR.getItemLifespanValue(),
                        amount,
                        amount,
                        PaymentStatus.CREATED,
                        invoices
                )
        );

        catalogueEntity = CatalogueEntity.builder()
                .category(1L)
                .price(amount)
                .id(9L)
                .itemName(catalogItemName)
                .build();
    }

    @Test
    void getRequisitionItemList() throws BusinessException {

        doNothing().when(requisitionService).validateRequisition(anyLong());
        when(itemRepository.findItemListByRequisitionId(anyLong())).thenReturn(requisitionItemListProjections);
        when(invoiceService.findInvoiceByRequisitionItemId(anyLong())).thenReturn(invoices);

        List<RequisitionItemListDTO> response = itemService.getRequisitionItemList(1L);

        assertThat(response).isNotEmpty();
        assertThat(response.get(0)).isNotNull();
        assertThat(response.get(0)).isExactlyInstanceOf(RequisitionItemListDTO.class);
        assertThat(response).isEqualTo(requisitionItemList);

    }

    @Test
    void getRequisitionItemList_ThrowsNotFound() throws BusinessException {

        doThrow(new BusinessException(HttpStatus.NOT_FOUND, ResponseMessage.REQUISITION_NOT_FOUND)).when(requisitionService).validateRequisition(anyLong());

        assertThatThrownBy(() -> itemService.getRequisitionItemList(1L))
                .isExactlyInstanceOf(BusinessException.class)
                .hasMessage(ResponseMessage.REQUISITION_NOT_FOUND);

    }

    @Test
    void cancelAllItem() throws BusinessException {

        requisitionEntity.setRequestStatus(RequisitionStatus.IN_PROGRESS);
        requisitionEntity.setRequisitionItems(requisitionEntity.getRequisitionItems()
                .stream()
                .peek(i -> i.setPaymentStatus(PaymentStatus.CREATED))
                .collect(Collectors.toSet()));
        when(requisitionService.findRequisitionById(anyLong())).thenReturn(requisitionEntity);
        assertAll(() -> itemService.cancelItem(true, 1L, null, userId));

    }

    @Test
    void cancelAllItem_ThrowsStatusNotMatch() throws BusinessException {

        requisitionEntity.setRequestStatus(RequisitionStatus.SUBMITTED);
        requisitionEntity.setRequisitionItems(requisitionEntity.getRequisitionItems()
                .stream()
                .peek(i -> i.setPaymentStatus(PaymentStatus.CREATED))
                .collect(Collectors.toSet()));
        when(requisitionService.findRequisitionById(anyLong())).thenReturn(requisitionEntity);
        assertThatThrownBy(() -> itemService.cancelItem(true, 1L, null, userId))
                .isExactlyInstanceOf(BusinessException.class)
                .hasMessage(ResponseMessage.STATUS_NOT_MATCH);

    }

    @Test
    void cancelItem() throws BusinessException {

        requisitionEntity.setRequestStatus(RequisitionStatus.IN_PROGRESS);
        requisitionItemEntity.setItemId(2L);
        requisitionEntity.getRequisitionItems().add(requisitionItemEntity);
        requisitionEntity.setRequisitionItems(requisitionEntity.getRequisitionItems()
                .stream()
                .peek(i -> i.setPaymentStatus(PaymentStatus.CREATED))
                .collect(Collectors.toSet()));
        when(requisitionService.findRequisitionById(anyLong())).thenReturn(requisitionEntity);
        assertAll(() -> itemService.cancelItem(false, 1L, 1L, userId));

    }

    @Test
    void utilizePayment() throws BusinessException {
        requisitionEntity.setRequestStatus(RequisitionStatus.IN_PROGRESS);
        when(requisitionService.findRequisitionById(anyLong())).thenReturn(requisitionEntity);
        when(catalogueService.findCatalogueById(anyLong())).thenReturn(catalogueEntity);

        assertAll(() -> itemService.utilizePayment(1L, 1L, amount));
    }

    @Test
    void itemExist() {
        assertAll(() -> itemService.itemExist(requisitionEntity, 1L));
    }

    @Test
    void itemExist_ThrowsNotFound() {
        assertThatThrownBy(() -> itemService.itemExist(requisitionEntity, 10L))
                .isExactlyInstanceOf(BusinessException.class)
                .hasMessage(ResponseMessage.ITEM_NOT_FOUND);
    }

    @Test
    void uploadPayment() {
        assertAll(() -> itemService.uploadPayment(1L, 1L, RandomStringUtils.randomAlphanumeric(5)));
    }

    @Test
    void calculateActualizationSmallerRequestAmount(){
        requisitionEntity.setRequestAmount(new BigDecimal("8000.0000"));
        requisitionEntity.getRequisitionItems().clear();
        requisitionEntity.getRequisitionItems().add(new RequisitionItemEntity(
                1L,
                requisitionEntity,
                1L,
                1,
                timestamp,
                PaymentStatus.ACTUALIZED,
                new BigDecimal("8000.0000"),
                new BigDecimal("8000.0000"),
                null,
                null));

        assertThat(itemService.calculateActualization(requisitionEntity)).isEqualTo(new BigDecimal("8000.0000"));
    }

}

