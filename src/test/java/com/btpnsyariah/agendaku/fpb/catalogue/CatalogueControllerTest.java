package com.btpnsyariah.agendaku.fpb.catalogue;

import com.btpnsyariah.agendaku.fpb.exception.BusinessException;
import com.btpnsyariah.agendaku.fpb.service.LoggingService;
import org.codehaus.jackson.map.ObjectMapper;
import org.hibernate.exception.JDBCConnectionException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;


@WebMvcTest(CatalogueController.class)
@AutoConfigureMockMvc(addFilters = false)
@ExtendWith(MockitoExtension.class)
@ActiveProfiles("dev")
class CatalogueControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    Logger logger;

    @MockBean
    private CatalogueServiceImpl catalogueService;

    @MockBean
    private LoggingService loggingService;

    private final ObjectMapper mapper = new ObjectMapper();

    private CatalogueEntity catalogue;

    private CatalogueRequestDTO catalogueRequestDTO;

    private CatalogueCategoryDTO catalogueCategoryDTO;

    private String userId;

    private final List<CatalogueEntity> catalogueEntityList = new ArrayList<>();

    private final List<CatalogueCategoryEntity> catalogueCategoryList = new ArrayList<>();

    private final List<CatalogueRequestDTO> catalogueRequestDTOList = new ArrayList<>();

    private final List<CatalogueCategoryDTO> catalogueCategoryDTOList = new ArrayList<>();

    private final Set<String> categories = new HashSet<>(Arrays.asList("Furniture", "Office Supply"));


    private Pageable pageable;

    private ResponseEntity responseEntity;

    private PageImpl page;


    @BeforeEach
    public void setUp() {
        catalogue = new CatalogueEntity(
                2L,
                2L,
                ItemLifespan.ABOVE_1_YEAR,
                "Spring Bed",
                "",
                "Unit",
                new BigDecimal("1221250.0000"),
                "MIT012",
                new Timestamp(new Date().getTime())
        );

        CatalogueCategoryEntity catalogueCategory = new CatalogueCategoryEntity(
                2L,
                "Furniture",
                "MIT012",
                new Timestamp(new Date().getTime())
        );

        catalogueRequestDTO = new CatalogueRequestDTO(
                1L,
                ItemLifespan.ABOVE_1_YEAR,
                "Spring Bed",
                "",
                "Unit",
                new BigDecimal("1221250.0000")
        );

        catalogueCategoryDTO = new CatalogueCategoryDTO(
                "Furniture",
                "MIT012",
                new Timestamp(new Date().getTime())
        );

        userId = "MIT012";
        catalogueEntityList.add(catalogue);
        catalogueCategoryList.add(catalogueCategory);
        catalogueRequestDTOList.add(catalogueRequestDTO);
        catalogueCategoryDTOList.add(catalogueCategoryDTO);

        pageable = new Pageable() {
            @Override
            public int getPageNumber() {
                return 0;
            }

            @Override
            public int getPageSize() {
                return 10;
            }

            @Override
            public long getOffset() {
                return 0;
            }

            @Override
            public Sort getSort() {
                return null;
            }

            @Override
            public Pageable next() {
                return null;
            }

            @Override
            public Pageable previousOrFirst() {
                return null;
            }

            @Override
            public Pageable first() {
                return null;
            }

            @Override
            public Pageable withPage(int pageNumber) {
                return null;
            }

            @Override
            public boolean hasPrevious() {
                return false;
            }
        };

        page = new PageImpl(
                catalogueRequestDTOList, pageable, Long.valueOf(0)
        );
    }

    @Test
    void testFetchItemCatalogue_SuccessReturnPagination() throws Exception {

        when(catalogueService.getItemCatalogue(any(Pageable.class))).thenReturn(page);

        mockMvc.perform(MockMvcRequestBuilders
                        .get("/item-catalogues")
                        .param("size","10")
                        .param("page","0")
                        .characterEncoding("utf-8"))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.totalPages").value(1))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.totalElements").value(1))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data.size").value(10));
    }

    @Test
    void testFetchItemCatalogue_ThrowsException() throws Exception {

        when(catalogueService.getItemCatalogue(any(Pageable.class))).thenThrow(ArrayIndexOutOfBoundsException.class);

        mockMvc.perform(MockMvcRequestBuilders
                        .get("/item-catalogues")
                        .param("size","10")
                        .param("page","0")
                        .characterEncoding("utf-8"))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().is5xxServerError())
                .andExpect(MockMvcResultMatchers.jsonPath("$.success").value(false))
                .andExpect(MockMvcResultMatchers.jsonPath("$.message").value("Failed to fetch items : null"))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").exists());
    }

    @Test
    void testSaveItemCatalogue_ReturnSuccess() throws Exception {

        doNothing().when(catalogueService).persistItemCatalogue(anyString(),any());

        mockMvc.perform(MockMvcRequestBuilders
                        .post("/item-catalogues")
                        .header("UserId", userId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(mapper.writeValueAsString(catalogueRequestDTOList))
                        .characterEncoding("utf-8"))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andExpect(MockMvcResultMatchers.jsonPath("$.message").value("Items saved!"));

    }

    @Test
    void testSaveItemCatalogue_ThrowsException() throws Exception {

        doThrow(JDBCConnectionException.class).when(catalogueService).persistItemCatalogue(anyString(),any());

        mockMvc.perform(MockMvcRequestBuilders
                        .post("/item-catalogues")
                        .header("UserId", userId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(mapper.writeValueAsString(catalogueRequestDTOList))
                        .characterEncoding("utf-8"))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().is5xxServerError())
                .andExpect(MockMvcResultMatchers.jsonPath("$.success").value(false))
                .andExpect(MockMvcResultMatchers.jsonPath("$.message").value("Failed to save items : null"))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").exists());

    }

    @Test
    void testUpdateItemCatalogue_ReturnSuccess() throws Exception {

        doNothing().when(catalogueService).updateItemCatalogue(anyString(), anyLong(), any());

        mockMvc.perform(MockMvcRequestBuilders
                        .put("/item-catalogues/1")
                        .header("UserId", userId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(mapper.writeValueAsString(catalogueRequestDTO))
                        .characterEncoding("utf-8"))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andExpect(MockMvcResultMatchers.jsonPath("$.message").value("Item updated!"));

    }

    @Test
    void testUpdateItemCatalogue_ThrowsNotFoundException() throws Exception {

        doThrow(new BusinessException(HttpStatus.NOT_FOUND, "Item not found with id : " + 1L)).when(catalogueService).updateItemCatalogue(anyString(), anyLong(), any());

        mockMvc.perform(MockMvcRequestBuilders
                        .put("/item-catalogues/1")
                        .header("UserId", userId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(mapper.writeValueAsString(catalogueRequestDTO))
                        .characterEncoding("utf-8"))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().is4xxClientError())
                .andExpect(MockMvcResultMatchers.jsonPath("$.success").value(false))
                .andExpect(MockMvcResultMatchers.jsonPath("$.message").value("Failed to update item : Item not found with id : 1"))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").exists());

    }

    @Test
    void testUpdateItemCatalogue_ThrowsException() throws Exception {

        doThrow(JDBCConnectionException.class).when(catalogueService).updateItemCatalogue(anyString(), anyLong(), any());

        mockMvc.perform(MockMvcRequestBuilders
                        .put("/item-catalogues/1")
                        .header("UserId", userId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(mapper.writeValueAsString(catalogueRequestDTO))
                        .characterEncoding("utf-8"))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().is5xxServerError())
                .andExpect(MockMvcResultMatchers.jsonPath("$.success").value(false))
                .andExpect(MockMvcResultMatchers.jsonPath("$.message").value("Failed to update item : null"))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").exists());

    }

    @Test
    void testDeleteItemCatalogue_ReturnSuccess() throws Exception {

        doNothing().when(catalogueService).deleteItem(anyString(), anyLong());

        mockMvc.perform(MockMvcRequestBuilders
                        .delete("/item-catalogues/1")
                        .header("UserId", userId)
                        .characterEncoding("utf-8"))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andExpect(MockMvcResultMatchers.jsonPath("$.message").value("Item deleted!"));

    }

    @Test
    void testDeleteItemCatalogue_ThrowsNotFoundException() throws Exception {

        doThrow(new BusinessException(HttpStatus.NOT_FOUND, "Item not found with id : " + 1L)).when(catalogueService).deleteItem(anyString(), anyLong());

        mockMvc.perform(MockMvcRequestBuilders
                        .delete("/item-catalogues/1")
                        .header("UserId", userId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .characterEncoding("utf-8"))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().is4xxClientError())
                .andExpect(MockMvcResultMatchers.jsonPath("$.success").value(false))
                .andExpect(MockMvcResultMatchers.jsonPath("$.message").value("Failed to delete item : Item not found with id : 1"))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").exists());

    }

    @Test
    void testDeleteItemCatalogue_ThrowsException() throws Exception {

        doThrow(JDBCConnectionException.class).when(catalogueService).deleteItem(anyString(), anyLong());

        mockMvc.perform(MockMvcRequestBuilders
                        .delete("/item-catalogues/1")
                        .header("UserId", userId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(mapper.writeValueAsString(catalogueRequestDTO))
                        .characterEncoding("utf-8"))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().is5xxServerError())
                .andExpect(MockMvcResultMatchers.jsonPath("$.success").value(false))
                .andExpect(MockMvcResultMatchers.jsonPath("$.message").value("Failed to delete item : null"))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").exists());

    }

    @Test
    void testGetCatalogueCategories_ReturnSuccess() throws Exception {

        when(catalogueService.getCatalogueCategories()).thenReturn(catalogueCategoryList);

        mockMvc.perform(MockMvcRequestBuilders
                        .get("/item-catalogues/categories")
                        .characterEncoding("utf-8"))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andExpect(MockMvcResultMatchers.jsonPath("$.success").value(true))
                .andExpect(MockMvcResultMatchers.jsonPath("$.message").value("Categories fetched"))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data[0].id").value(2L))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data[0].categoryName").value("Furniture"))
                .andExpect(MockMvcResultMatchers.jsonPath("$.data[0].createdBy").value("MIT012"));

    }

    @Test
    void testGetCatalogueCategories_ThrowsException() throws Exception {

        when(catalogueService.getCatalogueCategories()).thenThrow(JDBCConnectionException.class);

        mockMvc.perform(MockMvcRequestBuilders
                        .get("/item-catalogues/categories")
                        .param("size","10")
                        .param("page","0")
                        .characterEncoding("utf-8"))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().is5xxServerError())
                .andExpect(MockMvcResultMatchers.jsonPath("$.success").value(false))
                .andExpect(MockMvcResultMatchers.jsonPath("$.message").value("Failed to fetch categories : null"))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").exists());
    }

    @Test
    void testSaveCatalogueCategories_ReturnSuccess() throws Exception {

        doNothing().when(catalogueService).persistCatalogueCategories(anyString(),anySet());

        mockMvc.perform(MockMvcRequestBuilders
                        .post("/item-catalogues/categories")
                        .header("UserId", userId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(mapper.writeValueAsString(categories))
                        .characterEncoding("utf-8"))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andExpect(MockMvcResultMatchers.jsonPath("$.success").value(true))
                .andExpect(MockMvcResultMatchers.jsonPath("$.message").value("Categories saved!"));

    }

    @Test
    void testSaveCatalogueCategories_ThrowsConflict() throws Exception {

        doThrow(new BusinessException(HttpStatus.BAD_REQUEST, "One (or more) category already exist!")).when(catalogueService).persistCatalogueCategories(anyString(),anySet());

        mockMvc.perform(MockMvcRequestBuilders
                        .post("/item-catalogues/categories")
                        .header("UserId", userId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(mapper.writeValueAsString(categories))
                        .characterEncoding("utf-8"))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().is4xxClientError())
                .andExpect(MockMvcResultMatchers.jsonPath("$.success").value(false))
                .andExpect(MockMvcResultMatchers.jsonPath("$.message").value("Failed to save categories : One (or more) category already exist!"))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").exists());

    }

    @Test
    void testSaveCatalogueCategories_ThrowsInvalidValue() throws Exception {

        doThrow(new BusinessException(HttpStatus.CONFLICT, "Characters and numbers are not allowed!")).when(catalogueService).persistCatalogueCategories(anyString(),anySet());

        mockMvc.perform(MockMvcRequestBuilders
                        .post("/item-catalogues/categories")
                        .header("UserId", userId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(mapper.writeValueAsString(categories))
                        .characterEncoding("utf-8"))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().is4xxClientError())
                .andExpect(MockMvcResultMatchers.jsonPath("$.success").value(false))
                .andExpect(MockMvcResultMatchers.jsonPath("$.message").value("Failed to save categories : Characters and numbers are not allowed!"))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").exists());

    }

    @Test
    void testSaveCatalogueCategories_ThrowsException() throws Exception {

        doThrow(JDBCConnectionException.class).when(catalogueService).persistCatalogueCategories(anyString(),anySet());

        mockMvc.perform(MockMvcRequestBuilders
                        .post("/item-catalogues/categories")
                        .header("UserId", userId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(mapper.writeValueAsString(categories))
                        .characterEncoding("utf-8"))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().is5xxServerError())
                .andExpect(MockMvcResultMatchers.jsonPath("$.success").value(false))
                .andExpect(MockMvcResultMatchers.jsonPath("$.message").value("Failed to save categories : null"))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").exists());

    }

    @Test
    void testUpdateCatalogueCategory_ReturnSuccess() throws Exception {

        doNothing().when(catalogueService).updateCatalogueCategory(anyString(), anyLong(), any());

        mockMvc.perform(MockMvcRequestBuilders
                        .put("/item-catalogues/categories/1")
                        .header("UserId", userId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(mapper.writeValueAsString(catalogueCategoryDTO))
                        .characterEncoding("utf-8"))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andExpect(MockMvcResultMatchers.jsonPath("$.success").value(true))
                .andExpect(MockMvcResultMatchers.jsonPath("$.message").value("Category updated!"));

    }

    @Test
    void testUpdateCatalogueCategory_ThrowsNotFoundException() throws Exception {

        doThrow(new BusinessException(HttpStatus.NOT_FOUND, "Category not found with id : " + 1L)).when(catalogueService).updateCatalogueCategory(anyString(), anyLong(), any());

        mockMvc.perform(MockMvcRequestBuilders
                        .put("/item-catalogues/categories/1")
                        .header("UserId", userId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(mapper.writeValueAsString(catalogueCategoryDTO))
                        .characterEncoding("utf-8"))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().is4xxClientError())
                .andExpect(MockMvcResultMatchers.jsonPath("$.success").value(false))
                .andExpect(MockMvcResultMatchers.jsonPath("$.message").value("Failed to update category : Category not found with id : 1"))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").exists());

    }

    @Test
    void testUpdateCatalogueCategory_ThrowsException() throws Exception {

        doThrow(JDBCConnectionException.class).when(catalogueService).updateCatalogueCategory(anyString(), anyLong(), any());

        mockMvc.perform(MockMvcRequestBuilders
                        .put("/item-catalogues/categories/1")
                        .header("UserId", userId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(mapper.writeValueAsString(catalogueCategoryDTO))
                        .characterEncoding("utf-8"))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().is5xxServerError())
                .andExpect(MockMvcResultMatchers.jsonPath("$.success").value(false))
                .andExpect(MockMvcResultMatchers.jsonPath("$.message").value("Failed to update category : null"))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").exists());

    }



    @Test
    void testDeleteCatalogueCategory_ReturnSuccess() throws Exception {

        doNothing().when(catalogueService).deleteCategory(anyString(), anyLong());

        mockMvc.perform(MockMvcRequestBuilders
                        .delete("/item-catalogues/categories/1")
                        .header("UserId", userId)
                        .characterEncoding("utf-8"))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().is2xxSuccessful())
                .andExpect(MockMvcResultMatchers.jsonPath("$.success").value(true))
                .andExpect(MockMvcResultMatchers.jsonPath("$.message").value("Category deleted!"));

    }

    @Test
    void testDeleteCatalogueCategory_ThrowsNotFoundException() throws Exception {

        doThrow(new BusinessException(HttpStatus.NOT_FOUND, "Category not found with id : " + 1L)).when(catalogueService).deleteCategory(anyString(), anyLong());

        mockMvc.perform(MockMvcRequestBuilders
                        .delete("/item-catalogues/categories/1")
                        .header("UserId", userId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .characterEncoding("utf-8"))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().is4xxClientError())
                .andExpect(MockMvcResultMatchers.jsonPath("$.success").value(false))
                .andExpect(MockMvcResultMatchers.jsonPath("$.message").value("Failed to delete category : Category not found with id : 1"))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").exists());

    }

    @Test
    void testDeleteCatalogueCategory_ThrowsException() throws Exception {

        doThrow(JDBCConnectionException.class).when(catalogueService).deleteCategory(anyString(), anyLong());

        mockMvc.perform(MockMvcRequestBuilders
                        .delete("/item-catalogues/categories/1")
                        .header("UserId", userId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .characterEncoding("utf-8"))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().is5xxServerError())
                .andExpect(MockMvcResultMatchers.jsonPath("$.success").value(false))
                .andExpect(MockMvcResultMatchers.jsonPath("$.message").value("Failed to delete category : null"))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").exists());

    }

}
