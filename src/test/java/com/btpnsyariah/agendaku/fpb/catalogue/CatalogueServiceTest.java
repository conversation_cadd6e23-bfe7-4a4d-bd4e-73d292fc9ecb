package com.btpnsyariah.agendaku.fpb.catalogue;

import com.btpnsyariah.agendaku.fpb.exception.BusinessException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.assertAll;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@ActiveProfiles("dev")
class CatalogueServiceTest {

    @Autowired
    private MockMvc mockMvc;

    @InjectMocks
    private CatalogueServiceImpl catalogueServiceImpl;

    @Mock
    private CatalogueRepository catalogueRepository;

    @Mock
    private CatalogueCategoryRepository catalogueCategoryRepository;

    private CatalogueEntity catalogue;

    private CatalogueCategoryEntity catalogueCategory;

    private CatalogueRequestDTO catalogueRequestDTO;

    private CatalogueResponseDTO catalogueResponseDTO;

    private CatalogueCategoryDTO catalogueCategoryDTO;

    private String userId;

    private final List<CatalogueEntity> catalogueEntityList = new ArrayList<>();

    private final List<CatalogueDTO> catalogueDTOList = new ArrayList<>();

    private final List<CatalogueCategoryEntity> catalogueCategoryList = new ArrayList<>();

    private final List<CatalogueRequestDTO> catalogueRequestDTOList = new ArrayList<>();

    private final List<CatalogueResponseDTO> catalogueResponseDTOList = new ArrayList<>();

    private final List<CatalogueCategoryDTO> catalogueCategoryDTOList = new ArrayList<>();

    private Set<String> categories = new HashSet<>(Arrays.asList("Furniture", "Office Supply"));

    private Pageable pageable;

    private ResponseEntity responseEntity;

    private PageImpl page;


    @BeforeEach
    public void setUp() {

        CatalogueDTO catalogueDTO = new CatalogueDTO() {
            @Override
            public Long getId() {
                return 2L;
            }

            @Override
            public String getCategory() {
                return "Furniture";
            }

            @Override
            public ItemLifespan getLifespan() {
                return ItemLifespan.ABOVE_1_YEAR;
            }

            @Override
            public String getItemName() {
                return "Spring Bed";
            }

            @Override
            public String getSpecifications() {
                return "";
            }

            @Override
            public String getMeasurementUnit() {
                return "Unit";
            }

            @Override
            public BigDecimal getPrice() {
                return new BigDecimal("1221250.0000");
            }

            @Override
            public String getCreatedBy() {
                return "MIT012";
            }

            @Override
            public Timestamp getCreatedDate() {
                return new Timestamp(new Date().getTime());
            }

            @Override
            public Timestamp getUpdatedDate() {
                return null;
            }

            @Override
            public String getUpdatedBy() {
                return null;
            }
        };

        CatalogueEntity.builder().id(2L).category(2L).lifespan(ItemLifespan.ABOVE_1_YEAR).itemName("Spring Bed").specifications("").measurementUnit("Unit").price(new BigDecimal("1221250.0000")).createdBy("MIT012").createdDate(new Timestamp(new Date().getTime()));

        catalogue = new CatalogueEntity();

        catalogue = new CatalogueEntity(
                2L,
                2L,
                ItemLifespan.ABOVE_1_YEAR,
                "Spring Bed",
                "",
                "Unit",
                new BigDecimal("1221250.0000"),
                "MIT012",
                new Timestamp(new Date().getTime())
        );

        catalogue = new CatalogueEntity(
                2L,
                2L,
                ItemLifespan.ABOVE_1_YEAR,
                "Spring Bed",
                "",
                "Unit",
                new BigDecimal("1221250.0000"),
                "MIT012",
                new Timestamp(new Date().getTime()),
                null,
                null
        );

        CatalogueCategoryEntity.builder().id(1L).categoryName("").createdBy("").createdDate(null).build();

        catalogueCategory = new CatalogueCategoryEntity();

        catalogueCategory = new CatalogueCategoryEntity(
                2L,
                "Furniture",
                "MIT012",
                new Timestamp(new Date().getTime())
        );

        catalogueCategory = new CatalogueCategoryEntity(
                2L,
                "Furniture",
                "MIT012",
                new Timestamp(new Date().getTime()),
                null,
                null
        );

        CatalogueRequestDTO.builder().category(2L).lifespan(ItemLifespan.ABOVE_1_YEAR).itemName("").specifications("").measurementUnit("").price(null);

        catalogueRequestDTO = new CatalogueRequestDTO(
                2L,
                ItemLifespan.ABOVE_1_YEAR,
                "Spring Bed",
                "",
                "Unit",
                new BigDecimal("1221250.0000")
        );

        catalogueResponseDTO = new CatalogueResponseDTO();

        catalogueResponseDTO = new CatalogueResponseDTO(
                2L,
                "Furniture",
                ItemLifespan.ABOVE_1_YEAR.getItemLifespanValue(),
                "Spring Bed",
                "",
                "Unit",
                new BigDecimal("1221250.0000"),
                "MIT012",
                new Timestamp(new Date().getTime()),
                null,
                null
        );

        catalogueCategoryDTO = new CatalogueCategoryDTO();

        CatalogueCategoryDTO.builder().categoryName("").createdBy("").createdDate(null).updatedDate(null).updatedBy("").build();

        catalogueCategoryDTO = new CatalogueCategoryDTO(
                "Furniture",
                "MIT012",
                new Timestamp(new Date().getTime())
        );

        userId = "MIT012";
        catalogueCategoryList.add(catalogueCategory);
        catalogueCategoryDTOList.add(catalogueCategoryDTO);
        pageable = new Pageable() {
            @Override
            public int getPageNumber() {
                return 0;
            }

            @Override
            public int getPageSize() {
                return 10;
            }

            @Override
            public long getOffset() {
                return 0;
            }

            @Override
            public Sort getSort() {
                return Sort.unsorted();
            }

            @Override
            public Pageable next() {
                return null;
            }

            @Override
            public Pageable previousOrFirst() {
                return null;
            }

            @Override
            public Pageable first() {
                return null;
            }

            @Override
            public Pageable withPage(int pageNumber) {
                return null;
            }

            @Override
            public boolean hasPrevious() {
                return false;
            }
        };

        for(int i = 1; i <= 15; i++) {
            catalogueResponseDTOList.add(catalogueResponseDTO);
            catalogueRequestDTOList.add(catalogueRequestDTO);
            catalogueEntityList.add(catalogue);
            catalogueDTOList.add(catalogueDTO);
        }

        page = new PageImpl(
                catalogueRequestDTOList, pageable, 0L
        );
    }

    @Test
    void testFetchCataloguePagination(){

        when(catalogueRepository.findAllCatalogues()).thenReturn(catalogueDTOList);

        PageImpl response = catalogueServiceImpl.getItemCatalogue(pageable);

        assertThat(response).isNotNull();
        assertThat(response.getTotalPages()).isSameAs(2);
        assertThat(response.getTotalElements()).isSameAs(15L);
        assertThat(response.getSize()).isSameAs(10);
        assertThat(response.getContent().get(0)).isExactlyInstanceOf(CatalogueResponseDTO.class);
    }

    @Test
    void testSaveCatalogue() {
        assertAll(() -> catalogueServiceImpl.persistItemCatalogue(userId, catalogueRequestDTOList));
    }

    @Test
    void updateCatalogue() {
        when(catalogueRepository.findById(1L)).thenReturn(Optional.of(catalogue));
        assertAll(() -> catalogueServiceImpl.updateItemCatalogue(userId, 1L, catalogueRequestDTO));
    }

    @Test
    void updateCatalogueThrowsNotFound() {
        when(catalogueRepository.findById(1L)).thenReturn(Optional.empty());
        assertThatThrownBy(() -> catalogueServiceImpl.updateItemCatalogue(userId, 1L, catalogueRequestDTO))
                .isExactlyInstanceOf(BusinessException.class)
                .hasMessage("Item not found with id : 1");
    }

    @Test
    void deleteCatalogue() {
        when(catalogueRepository.findById(1L)).thenReturn(Optional.of(catalogue));
        assertAll(() -> catalogueServiceImpl.deleteItem(userId, 1L));
    }

    @Test
    void deleteCatalogueThrowsNotFound() {
        when(catalogueRepository.findById(1L)).thenReturn(Optional.empty());
        assertThatThrownBy(() -> catalogueServiceImpl.deleteItem(userId, 1L))
                .isExactlyInstanceOf(BusinessException.class)
                .hasMessage("Item not found with id : 1");
    }

    @Test
    void testFetchCatalogueCategories(){

        when(catalogueCategoryRepository.findAll()).thenReturn(catalogueCategoryList);

        List<CatalogueCategoryEntity> response = catalogueServiceImpl.getCatalogueCategories();

        assertThat(response).isNotNull();
        assertThat(response.size()).isSameAs(1);
        assertThat(response.get(0)).isSameAs(catalogueCategory);
    }

    @Test
    void testSaveCatalogueCategory() {
        assertAll(() -> catalogueServiceImpl.persistCatalogueCategories(userId, categories));
    }

    @Test
    void testSaveCatalogueCategoryThrowsConflict() {
        when(catalogueCategoryRepository.findAll()).thenReturn(catalogueCategoryList);
        assertThatThrownBy(() -> catalogueServiceImpl.persistCatalogueCategories(userId, categories))
                .isExactlyInstanceOf(BusinessException.class)
                .hasMessage("Furniture already exist!");
    }

    @Test
    void testSaveCatalogueCategoryThrowsBadRequest() {
        categories = new HashSet<>(Arrays.asList("Furniture%", "Office&Supply"));
        assertThatThrownBy(() -> catalogueServiceImpl.persistCatalogueCategories(userId, categories))
                .isExactlyInstanceOf(BusinessException.class)
                .hasMessage("Characters and numbers are not allowed!");
    }

    @Test
    void updateCatalogueCategory() {
        when(catalogueCategoryRepository.findById(1L)).thenReturn(Optional.of(catalogueCategory));
        assertAll(() -> catalogueServiceImpl.updateCatalogueCategory(userId, 1L, catalogueCategoryDTO));
    }

    @Test
    void updateCatalogueCategoryThrowsNotFound() {
        when(catalogueCategoryRepository.findById(1L)).thenReturn(Optional.empty());
        assertThatThrownBy(() -> catalogueServiceImpl.updateCatalogueCategory(userId, 1L, catalogueCategoryDTO))
                .isExactlyInstanceOf(BusinessException.class)
                .hasMessage("Category not found with id : 1");
    }

    @Test
    void updateCatalogueCategoryThrowsBadRequest() {
        catalogueCategoryDTO.setCategoryName("Furniture%");
        assertThatThrownBy(() -> catalogueServiceImpl.updateCatalogueCategory(userId, 1L, catalogueCategoryDTO))
                .isExactlyInstanceOf(BusinessException.class)
                .hasMessage("Characters and numbers are not allowed!");
    }

    @Test
    void deleteCatalogueCategory() {
        when(catalogueCategoryRepository.findById(1L)).thenReturn(Optional.of(catalogueCategory));
        assertAll(() -> catalogueServiceImpl.deleteCategory(userId, 1L));
    }

    @Test
    void deleteCatalogueCategoryThrowsNotFound() {
        when(catalogueCategoryRepository.findById(1L)).thenReturn(Optional.empty());
        assertThatThrownBy(() -> catalogueServiceImpl.deleteCategory(userId, 1L))
                .isExactlyInstanceOf(BusinessException.class)
                .hasMessage("Category not found with id : 1");
    }
}
