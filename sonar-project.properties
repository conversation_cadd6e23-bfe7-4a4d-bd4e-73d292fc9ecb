# must be unique in a given SonarQube instance
sonar.projectKey=com.btpnsyariah.agendaku.fpb

# --- optional properties ---

# defaults to project key
sonar.projectName=agendaku-fpb
# defaults to 'not provided'
sonar.projectVersion=${VERSION}
 
# Path is relative to the sonar-project.properties file. Defaults to .
sonar.sources=src
sonar.java.binaries=target/classes
# Encoding of the source code. Default is default system encoding
#sonar.sourceEncoding=UTF-8
