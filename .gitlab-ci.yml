variables:
  GROUP_NAME: "agendaku"
  APP_NAME: "agendaku-fpb"
  VERSION: "1.0.0-snapshot"

image: "docker:latest"

stages:
  - building
  - unit-testing
  - packaging
  - source-scanning
  - image-building
  - image-scanning
  - dirty-deploy
  - dirty-manage
  - dirty-testing
  - alpha-promotion
  - alpha-deploy
  - alpha-manage
  - alpha-testing
  - beta-promotion
  - beta-deploy
  - beta-manage
  - regresion-testing
  - nonfunc-testing
  - promote-production
  - release-production
  - prod-deploy
  - prod-manage
  - Mr-To-Master


include:
  - project: 'base/templates'
    ref: main
    file: '/Fix-Template/Java11-Terra-rosa.yml'
