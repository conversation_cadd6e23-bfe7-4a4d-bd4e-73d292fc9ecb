ELASTIC_APM_SERVICE_NAME=agendaku-fpb
ELASTIC_APM_ENVIRONMENT=beta
ELASTIC_APM_LOG_LEVEL=INFO
ELASTIC_APM_SERVER_URLS=http://10.7.79.25
ELASTIC_APM_APPLICATION_PACKAGES=com.btpns.agendaku.fpb

HEAP_MIN=32m
HEAP_MAX=256m
HEAP_STACK=228k

SERVER_PORT=8080
CONTEXT_PATH=/beta-fpb

IMAGE_STORAGE_PATH=https://storage-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com/bucket03/agendaku/fpb/

KAFKA_SERVER_URL=kafka-beta.svc.soutdh.syariahbtpn.com:443
KAFKA_CONCURENCY_COUNT=1

CM_POST_LB_URL=https://agendaku.apps.btpnsyariah.com/beta-cash-management/v1/lbDailyTransaction/save/force/0

SPRINGDOC_UI_TOGGLE=true
SPRINGDOC_API_TOGGLE=true

LOMBOK_BASE_URL=https://api-beta-v2.apps.nww.syariahbtpn.com
ESB_LOMBOK_API_KEY=d2745ad134cf09a844d2d8413aa1ab2b

ENV=BETA
