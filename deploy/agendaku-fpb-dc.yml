---
kind: Template
apiVersion: v1
metadata:
  name: ${APP_NAME}-dc
  namespace: ${GROUP_NAME}-${STAGE}
labels:
  template: ${APP_NAME}-dc
  app: ${APP_NAME}
objects:
- kind: DeploymentConfig
  apiVersion: apps.openshift.io/v1
  metadata:
    name: ${APP_NAME}
    namespace: ${GROUP_NAME}-${STAGE}
  spec:
    selector:
      app: ${APP_NAME}
    replicas: ${{REPLICAS_PODS}}
    template:
      metadata:
        labels:
          app: ${APP_NAME}
      spec:
        volumes:
          - name: kafka-ca
            secret:
              secretName: agendaku-kafka-ca-cert
        containers:
          - name: ${APP_NAME}
            image: "${OCP_INTERNAL_URL}/${GROUP_NAME}-${STAGE}/${APP_NAME}:${OCP_BUILD_TAG}"
            volumeMounts:
              - name: kafka-ca
                mountPath: /etc/tls/kafka-ca-certs/
            ports:
              - containerPort: 8080
            resources:
              limits:
                cpu: ${{LIMIT_CPU}}
                memory: ${{LIMIT_MEMORY}}
              requests:
                cpu: 100m
                memory: 64M
            envFrom:
              - configMapRef:
                  name: ${APP_NAME}-env
              - secretRef:
                  name: ${APP_NAME}-secrets
parameters:
  - name: APP_NAME
  - name: REPLICAS_PODS
  - name: LIMIT_CPU
  - name: LIMIT_MEMORY
  - name: CI_COMMIT_SHORT_SHA
    value: latest
  - name: STAGE
  - name: GROUP_NAME
  - name: OCP_REGISTRY_URL
  - name: OCP_BUILD_TAG
    value: latest
  - name: OCP_INTERNAL_URL
    value: image-registry.openshift-image-registry.svc:5000
