ELASTIC_APM_SERVICE_NAME=agendaku-fpb
ELASTIC_APM_ENVIRONMENT=dirty
ELASTIC_APM_LOG_LEVEL=INFO
ELASTIC_APM_SERVER_URLS=http://apm-server-jaeger-apm-http.elastic-system.svc.cluster.local:8200
ELASTIC_APM_APPLICATION_PACKAGES=com.btpns.agendaku.fpb

HEAP_MIN=32m
HEAP_MAX=256m
HEAP_STACK=228k

SERVER_PORT=8080
CONTEXT_PATH=/dirty-fpb

IMAGE_STORAGE_PATH=https://storage-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com/bucket01/agendaku/fpb/

KAFKA_SERVER_URL=dirty-kafka-bootstrap.amq-streams-dev.svc.cluster.local:9093
KAFKA_CONCURENCY_COUNT=1

CM_POST_LB_URL=https://cash-management-agendaku-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com/dev-cash-management/v1/lbDailyTransaction/save/force/0

SPRINGDOC_UI_TOGGLE=true
SPRINGDOC_API_TOGGLE=true

LOMBOK_BASE_URL=https://api-dirty-v2.apps.btpnsdev1.c3vu.p1.openshiftapps.com
ESB_LOMBOK_API_KEY=d2745ad134cf09a844d2d8413aa1ab2b

ENV=DIRTY